import { useState, useRef, useEffect } from 'react';
import SpeechR<PERSON>ognition, { useSpeechRecognition } from 'react-speech-recognition';
import streamingApi from '../services/streamingApi';
import ResponseRenderer from './ResponseRenderer/ResponseRenderer';
import DownloadRenderer from './DownloadRenderer/DownloadRenderer';
import ExcelGenerator from '../utils/excelGenerator';
import EmailReportModal from './EmailReportModal/EmailReportModal';
import WaitingMessage from './WaitingMessage/WaitingMessage';
import authService from '../services/authService';
import '../assets/Style/ChatWindow.css';
import '../assets/Style/WelcomeScreen.css';

export default function ChatWindow({ conversation, onSendMessage }) {
  const [message, setMessage] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [emailModalOpen, setEmailModalOpen] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [editingMessageId, setEditingMessageId] = useState(null);
  const [editingText, setEditingText] = useState('');
  const [waitingState, setWaitingState] = useState({
    isWaiting: false,
    currentStep: 0,
    steps: [
      { message: 'Parsing user query...', icon: '🔍' },
      { message: 'Connecting to datasource...', icon: '🔗' },
      { message: 'Calling reporting agent...', icon: '📊' },
      { message: 'Processing response...', icon: '⚙️' },
      { message: 'Finalizing report...', icon: '✨' }
    ]
  });
  const [dynamicMessage, setDynamicMessage] = useState('');
  const [postResponseWait, setPostResponseWait] = useState(false);
  const [isProcessingResponse, setIsProcessingResponse] = useState(false);
  const messagesEndRef = useRef(null);
  const textareaRef = useRef(null);

  // Get current authenticated user
  const currentUser = authService.getCurrentUser() || {
    email: '<EMAIL>',
    name: 'John Doe'
  };

  // Debug logging
  console.log('ChatWindow rendered with conversation:', conversation);
  console.log('Conversation messages count:', conversation?.messages?.length || 0);
  
  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition
  } = useSpeechRecognition();

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversation?.messages]);

  useEffect(() => {
    if (transcript) {
      setMessage(transcript);
    }
  }, [transcript]);

  // Auto-resize textarea
  const handleTextareaChange = (e) => {
    setMessage(e.target.value);
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
    }
  };

  // Waiting message management
  const startWaitingSequence = () => {
    setWaitingState(prev => ({
      ...prev,
      isWaiting: true,
      currentStep: 0
    }));
    setDynamicMessage(''); // Reset dynamic message
    setIsProcessingResponse(false);
  };

  const updateWaitingStep = (step) => {
    setWaitingState(prev => ({
      ...prev,
      currentStep: step
    }));
  };

  const endWaitingSequence = () => {
    setWaitingState(prev => ({
      ...prev,
      isWaiting: false,
      currentStep: 0
    }));
    setDynamicMessage(''); // Reset dynamic message
    setIsProcessingResponse(false);
  };

  const startPostResponseWait = async () => {
    setPostResponseWait(true);
    setIsProcessingResponse(true);
    setWaitingState(prev => ({
      ...prev,
      isWaiting: true,
      currentStep: 0,
      steps: [
        { message: 'Processing final response...', icon: '⚡' },
        { message: 'Optimizing display...', icon: '🎨' },
        { message: 'Ready to display!', icon: '✅' }
      ]
    }));

    // Progress through post-response steps
    for (let i = 0; i < 3; i++) {
      await new Promise(resolve => setTimeout(resolve, 3000));
      if (i < 2) {
        updateWaitingStep(i + 1);
      }
    }

    // Final delay before showing result
    await new Promise(resolve => setTimeout(resolve, 1000));

    setPostResponseWait(false);
    endWaitingSequence();
  };

  // Function to normalize numbers with commas for backend processing
  const normalizeNumbersInText = (text) => {
    // Enhanced pattern to match various number formats with commas
    // Matches patterns like: 1,00,000 (Indian) or 1,000,000 (International) or 10,00,000
    // Also handles decimal numbers like 1,00,000.50
    const numberWithCommasPattern = /\b\d{1,3}(?:,\d{2,3})*(?:\.\d+)?\b/g;

    return text.replace(numberWithCommasPattern, (match) => {
      // Check if this looks like a valid number with commas
      const hasValidCommaPattern = /^\d{1,3}(?:,\d{2,3})+/.test(match);

      if (hasValidCommaPattern) {
        // Remove all commas and return the clean number
        const cleanNumber = match.replace(/,/g, '');
        console.log(`Normalized number: ${match} -> ${cleanNumber}`);
        return cleanNumber;
      }

      // Return original if it doesn't match expected comma patterns
      return match;
    });
  };

  const handleSend = async () => {
    if (message.trim() && !isLoading) {
      const userMessage = message.trim();
      // Don't clear message immediately - wait for successful send
      resetTranscript();

      try {
        setIsLoading(true);

        // Start waiting sequence
        startWaitingSequence();

        // Add user message to chat immediately with proper structure
        const userMessageObj = {
          id: 'user_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
          text: userMessage,
          timestamp: new Date(),
          success: true,
          isStructured: false,
          originalQuery: userMessage
        };

        console.log('ChatWindow: Adding user message:', userMessageObj);
        onSendMessage(userMessageObj, 'user');
        console.log('ChatWindow: User message sent to parent');

        // Clear message only after successfully adding to conversation
        setMessage('');

        // Progress through waiting steps
        await new Promise(resolve => setTimeout(resolve, 1500));
        updateWaitingStep(1); // Connecting to datasource

        await new Promise(resolve => setTimeout(resolve, 1500));
        updateWaitingStep(2); // Calling reporting agent

        // Call the streaming API via service
        // Normalize numbers with commas before sending to backend
        const normalizedMessage = normalizeNumbersInText(userMessage);
        console.log('Original message:', userMessage);
        console.log('Normalized message for API:', normalizedMessage);

        // TODO: Temporarily commenting out processing message to fix user message override
        // const processingMessage = {
        //   id: 'processing_' + Date.now(),
        //   text: '🔄 Processing your request...',
        //   timestamp: new Date(),
        //   success: true,
        //   isStructured: false,
        //   isProcessing: true,
        //   originalQuery: userMessage
        // };
        // onSendMessage(processingMessage, 'assistant');

        updateWaitingStep(3); // Processing response
        setIsProcessingResponse(true); // Start blur effect on waiting message

        const result = await streamingApi.generateReport(normalizedMessage, 'excel', (progressData) => {
          console.log('Progress update:', progressData);

          // Update waiting step and dynamic message based on streaming progress
          if (progressData.type === 'metadata') {
            // Query parsed and metadata received
            updateWaitingStep(3);
            setDynamicMessage(`Processing: ${progressData.query || 'your request'}`);
          } else if (progressData.type === 'data') {
            // Data chunks being received
            updateWaitingStep(4); // Finalizing report
            if (progressData.chunk && progressData.chunk.sheet_name) {
              setDynamicMessage(`Loading ${progressData.chunk.sheet_name} data...`);
            } else {
              setDynamicMessage('Processing data chunks...');
            }
          } else if (progressData.type === 'complete') {
            // Report generation complete
            updateWaitingStep(4);
            setDynamicMessage('Report generation complete!');
            setIsProcessingResponse(false); // Stop blur effect
          } else if (progressData.message) {
            // Use any custom message from the API
            setDynamicMessage(progressData.message);
          }
        });
        console.log('Streaming API result:', result);
        console.log('Result type:', typeof result);
        console.log('Result keys:', Object.keys(result || {}));
        console.log('Result.success:', result?.success);
        console.log('Result.excelData:', result?.excelData);
        console.log('Result.chunks:', result?.chunks);
        console.log('Result.type:', result?.type);

        // Start post-response waiting period
        await startPostResponseWait();

        if (result.success && result.downloadInfo) {
          // Handle successful report generation with download info
          console.log('Processing successful report generation:', result);

          try {
            const responseMessage = {
              id: 'response_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
              text: result.message || 'Report generated successfully!',
              timestamp: new Date(),
              success: true,
              isStructured: false,
              downloadInfo: result.downloadInfo,
              metadata: result.metadata,
              originalQuery: userMessage
            };

            console.log('ChatWindow: Adding assistant response:', responseMessage);
            onSendMessage(responseMessage, 'assistant');
            console.log('ChatWindow: Assistant response sent to parent');
            console.log('Download info sent to UI:', result.downloadInfo);

          } catch (error) {
            console.error('Error processing download:', error);
            const errorMessage = {
              id: 'error_' + Date.now(),
              text: `📊 Report generated but download preparation failed: ${error.message}`,
              timestamp: new Date(),
              success: false,
              isStructured: false,
              originalQuery: userMessage
            };
            onSendMessage(errorMessage, 'assistant');
          }
        } else {
          // Handle unexpected response - show debug info
          console.log('Unexpected response format:', result);
          const errorMessage = {
            id: 'error_' + Date.now(),
            text: `⚠️ Unexpected response format.\n\nReceived: ${JSON.stringify(result, null, 2)}\n\nPlease check console for details.`,
            timestamp: new Date(),
            success: false,
            isStructured: false,
            originalQuery: userMessage
          };
          onSendMessage(errorMessage, 'assistant');
        }

      } catch (error) {
        console.error('API Error:', error);
        endWaitingSequence(); // End waiting on error
        const errorMessage = {
          id: 'error_' + Date.now(),
          text: 'Sorry, I encountered an error processing your request. Please try again.',
          timestamp: new Date(),
          success: false
        };
        onSendMessage(errorMessage, 'assistant');
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Test function to create a mock Excel download with exact backend format
  const addTestDownload = () => {
    try {
      console.log('Creating test download with exact backend format...');

      // Create test data matching exact backend response format
      const testStreamingData = {
        sheets: {
          "Sales Data": {
            headers: ["product", "sales", "revenue", "month"],
            rows: [
              ["Product A", 1200, 24000, "January"],
              ["Product B", 850, 17000, "January"],
              ["Product C", 950, 19000, "January"],
              ["Product A", 1350, 27000, "February"],
              ["Product B", 920, 18400, "February"],
              ["Product C", 1100, 22000, "February"]
            ]
          }
        },
        metadata: {
          title: "Test Sales Data Report",
          generated_at: new Date().toISOString()
        }
      };

      console.log('Test streaming data:', testStreamingData);

      // Generate Excel file using the same method as real data
      const filename = `Report__Test_Sales_Data__${Date.now()}.xlsx`;
      const excelInfo = ExcelGenerator.generateFromApiResponse(
        { streamingData: testStreamingData },
        filename
      );

      console.log('Test Excel generation result:', excelInfo);

      const downloadMessage = {
        text: `📊 Report generated successfully!\n\n📋 **Report: Test Sales Data**\n\nFile: ${excelInfo.filename}\nSize: ${(excelInfo.size / 1024).toFixed(1)} KB`,
        timestamp: new Date(),
        success: true,
        isStructured: false,
        originalQuery: "Test Sales Data",
        downloadInfo: excelInfo
      };

      console.log('Sending test download message:', downloadMessage);
      onSendMessage("Generate test Excel download", 'user');
      onSendMessage(downloadMessage, 'assistant');
    } catch (error) {
      console.error('Test Excel generation failed:', error);
      onSendMessage("Generate test Excel download", 'user');
      onSendMessage({
        text: `❌ Test Excel generation failed: ${error.message}`,
        timestamp: new Date(),
        success: false
      }, 'assistant');
    }
  };

  // Test function to add sample structured data
  const addTestData = () => {
    const testData = {
      "success": true,
      "result": {
        "success": true,
        "data": {
          "reports": [
            {"id": 1, "title": "Q1 Sales Report", "type": "sales", "date": "2024-01-15", "status": "completed"},
            {"id": 2, "title": "Marketing Analysis", "type": "marketing", "date": "2024-02-01", "status": "draft"}
          ],
          "metrics": [
            {"name": "revenue", "value": 150000, "period": "Q1", "trend": "up"},
            {"name": "customers", "value": 1250, "period": "Q1", "trend": "up"}
          ]
        },
        "query_type": "data_retrieval",
        "metadata": {"entities_found": ["sales data"], "data_sources_accessed": ["reports", "metrics"]},
        "error": null
      },
      "context": {"intent": "view_sales_data", "task_type": "query", "entities": ["sales data"], "confidence": 0.9, "parameters": {}},
      "agents_used": ["query_agent"],
      "execution_time": 4.6275827999998,
      "flow_id": null,
      "error": null
    };

    const assistantMessage = {
      text: testData,
      agents_used: ["query_agent"],
      timestamp: new Date(),
      success: true,
      isStructured: true,
      originalQuery: "test structured data"
    };

    onSendMessage("Show me test structured data", 'user');
    onSendMessage(assistantMessage, 'assistant');
  };

  // Test API connection directly
  const testApiDirect = async () => {
    try {
      console.log('Testing direct API call...');
      const response = await fetch('http://localhost:8000/query/stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: 'test query for debugging',
          output_format: 'json'
        })
      });

      console.log('Direct API Response:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      const text = await response.text();
      console.log('Raw response text:', text);

      try {
        const json = JSON.parse(text);
        console.log('Parsed JSON:', json);

        onSendMessage("Direct API test", 'user');
        onSendMessage({
          text: `✅ Direct API test successful!\n\nResponse: ${JSON.stringify(json, null, 2)}`,
          timestamp: new Date(),
          success: true,
          isStructured: false
        }, 'assistant');
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        onSendMessage("Direct API test", 'user');
        onSendMessage({
          text: `❌ API returned non-JSON response:\n\n${text}`,
          timestamp: new Date(),
          success: false,
          isStructured: false
        }, 'assistant');
      }
    } catch (error) {
      console.error('Direct API test failed:', error);
      onSendMessage("Direct API test", 'user');
      onSendMessage({
        text: `❌ API test failed: ${error.message}`,
        timestamp: new Date(),
        success: false,
        isStructured: false
      }, 'assistant');
    }
  };

  // Generate Excel from last API response
  const generateExcelFromLastResponse = () => {
    try {
      // Find the last assistant message with structured data
      const lastStructuredMessage = conversation.messages
        .filter(msg => msg.type === 'assistant' && (msg.isStructured || msg.text))
        .pop();

      if (!lastStructuredMessage) {
        onSendMessage("No data available for Excel generation", 'user');
        onSendMessage({
          text: '❌ No structured data found in recent messages to generate Excel from.',
          timestamp: new Date(),
          success: false
        }, 'assistant');
        return;
      }

      // Generate Excel from the message data
      const responseData = typeof lastStructuredMessage.text === 'object'
        ? lastStructuredMessage.text
        : { message: lastStructuredMessage.text, agents_used: lastStructuredMessage.agents_used };

      const excelInfo = ExcelGenerator.generateFromApiResponse(
        responseData,
        `manual_report_${Date.now()}.xlsx`
      );

      const downloadMessage = {
        text: `📊 Excel generated from last response!\n\nFile: ${excelInfo.filename}\nSize: ${(excelInfo.size / 1024).toFixed(1)} KB`,
        timestamp: new Date(),
        success: true,
        isStructured: false,
        downloadInfo: excelInfo
      };

      onSendMessage("Generate Excel from last response", 'user');
      onSendMessage(downloadMessage, 'assistant');

    } catch (error) {
      console.error('Manual Excel generation failed:', error);
      onSendMessage("Generate Excel from last response", 'user');
      onSendMessage({
        text: `❌ Excel generation failed: ${error.message}`,
        timestamp: new Date(),
        success: false
      }, 'assistant');
    }
  };

  // Test Excel generation specifically
  const testExcelGeneration = async () => {
    try {
      console.log('Testing Excel generation...');
      const response = await fetch('http://localhost:8000/query/stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: 'generate excel report with sample data',
          output_format: 'excel'
        })
      });

      console.log('Excel API Response:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      const contentType = response.headers.get('content-type');
      const contentDisposition = response.headers.get('content-disposition');

      console.log('Content analysis:', {
        contentType,
        contentDisposition,
        isExcel: contentType && contentType.includes('excel'),
        isAttachment: contentDisposition && contentDisposition.includes('attachment')
      });

      if (contentType && (contentType.includes('excel') || contentType.includes('spreadsheet'))) {
        const blob = await response.blob();
        const downloadUrl = URL.createObjectURL(blob);

        onSendMessage("Test Excel generation", 'user');
        onSendMessage({
          text: `✅ Excel file detected!\n\nSize: ${blob.size} bytes\nType: ${blob.type}`,
          timestamp: new Date(),
          success: true,
          downloadInfo: {
            downloadUrl,
            filename: 'test-report.xlsx',
            size: blob.size,
            format: 'excel'
          }
        }, 'assistant');
      } else {
        const text = await response.text();
        onSendMessage("Test Excel generation", 'user');
        onSendMessage({
          text: `❌ Expected Excel file but got:\n\nContent-Type: ${contentType}\nResponse: ${text.substring(0, 500)}...`,
          timestamp: new Date(),
          success: false
        }, 'assistant');
      }
    } catch (error) {
      console.error('Excel test failed:', error);
      onSendMessage("Test Excel generation", 'user');
      onSendMessage({
        text: `❌ Excel test failed: ${error.message}`,
        timestamp: new Date(),
        success: false
      }, 'assistant');
    }
  };

  const startListening = () => {
    setIsListening(true);
    resetTranscript();
    SpeechRecognition.startListening({ continuous: true });
  };

  const stopListening = () => {
    setIsListening(false);
    SpeechRecognition.stopListening();
  };

  // Handler functions for message actions
  const handleCopyMessage = async (text) => {
    try {
      const textToCopy = typeof text === 'string' ? text : JSON.stringify(text, null, 2);
      await navigator.clipboard.writeText(textToCopy);
      console.log('Message copied to clipboard');
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy message:', error);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = textToCopy;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
    }
  };

  const handleThumbsUp = (messageId) => {
    console.log('Thumbs up for message:', messageId);
    // TODO: Implement feedback tracking
    // Could send feedback to analytics or backend
  };

  const handleThumbsDown = (messageId) => {
    console.log('Thumbs down for message:', messageId);
    // TODO: Implement feedback tracking and possibly show feedback form
  };

  const handleReadAloud = (text) => {
    if ('speechSynthesis' in window) {
      // Stop any ongoing speech
      window.speechSynthesis.cancel();

      // Create new utterance
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.8;
      utterance.pitch = 1;
      utterance.volume = 1;

      // Speak the text
      window.speechSynthesis.speak(utterance);
      console.log('Reading aloud:', text.substring(0, 50) + '...');
    } else {
      alert('Text-to-speech is not supported in your browser');
    }
  };

  const handleEmailReport = (message) => {
    setSelectedMessage(message);
    setEmailModalOpen(true);
    console.log('Opening email modal for message:', message.id);
  };

  const handleEditMessage = (messageId, currentText) => {
    setEditingMessageId(messageId);
    setEditingText(currentText);

    // Use setTimeout to ensure the textarea is rendered before adjusting height
    setTimeout(() => {
      const textarea = document.querySelector('.user-edit-textarea');
      if (textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 'px';
      }
    }, 0);
  };

  const handleCancelEdit = () => {
    setEditingMessageId(null);
    setEditingText('');
  };

  const handleSaveEdit = () => {
    if (editingText.trim() && editingMessageId) {
      // Find the message and update it
      const updatedConversation = conversation.map(msg =>
        msg.id === editingMessageId
          ? { ...msg, text: editingText.trim() }
          : msg
      );

      // Update the conversation through parent component
      // Note: You might need to add an onUpdateMessage prop to handle this
      console.log('Saving edited message:', editingMessageId, editingText);

      // For now, we'll just reset the editing state
      // In a real implementation, you'd call onUpdateMessage(updatedConversation)
      setEditingMessageId(null);
      setEditingText('');
    }
  };

  const handleSuggestionClick = (suggestionText) => {
    setMessage(suggestionText);
    // Auto-focus the input
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
    console.log('Suggestion clicked:', suggestionText);
  };

  if (!browserSupportsSpeechRecognition) {
    return (
      <div className="chat-container">
        <div className="error-message">
          Browser doesn't support speech recognition. Please use Chrome or Edge.
        </div>
      </div>
    );
  }

  const hasMessages = conversation?.messages?.length > 0;

  return (
    <div className="chat-container">
      {!hasMessages ? (
        <div className="welcome-view">
          <div className="welcome-container">
            <h1 className="brand-title">Report Manager</h1>
            {/* Large centered input box for welcome screen */}
            <div className="welcome-input-container">
              <div className="welcome-input-wrapper-large">
                <textarea
                  ref={textareaRef}
                  value={message}
                  onChange={handleTextareaChange}
                  placeholder={isLoading ? 'Processing your request...' : (isListening ? 'Listening...' : 'Ask me to generate a report...')}
                  disabled={isLoading}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSend();
                    }
                  }}
                  className="chat-input-large"
                  rows="1"
                />

                {/* Tools positioned below the input area */}
                <div className="welcome-tools-bottom">
                  <div className="left-tools">
                    <button className="tool-btn" title="Add">
                      +
                    </button>
                    <button className="tool-btn" title="Tools">
                      <span className="tool-icon">⚙</span>
                      <span>Tools</span>
                    </button>
                    <button className="tool-btn" onClick={addTestDownload} title="Test Download">
                      🧪 Test
                    </button>
                  </div>
                  <div className="right-tools">
                    <button
                      className={`tool-btn voice-btn ${isListening ? 'listening' : ''}`}
                      onClick={isListening ? stopListening : startListening}
                      title={isListening ? 'Stop listening' : 'Start voice input'}
                    >
                      🎤
                    </button>
                    <button
                      className="tool-btn send-btn"
                      onClick={handleSend}
                      disabled={!message.trim()}
                      title="Send message"
                    >
                      ↗
                    </button>
                  </div>
                </div>
              </div>

              {/* Welcome screen disclaimer */}
              <div className="welcome-disclaimer">
                <p>ReportDesk can make mistakes. Check important info.</p>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="messages-area">
          {console.log('Rendering conversation:', conversation)}
          {console.log('Total messages:', conversation?.messages?.length || 0)}



          {(conversation?.messages || []).map((msg, index) => {
            console.log(`Message ${index}:`, msg);
            console.log(`Message type: ${msg.type}, text: ${msg.text?.substring(0, 50)}...`);
            console.log(`Has downloadInfo:`, !!msg.downloadInfo);
            console.log(`downloadInfo value:`, msg.downloadInfo);
            console.log(`downloadInfo type:`, typeof msg.downloadInfo);
            if (msg.downloadInfo) {
              console.log(`downloadInfo keys:`, Object.keys(msg.downloadInfo));
              console.log(`downloadInfo.downloadUrl:`, msg.downloadInfo.downloadUrl);
            }
            return (
            <div key={msg.id || `${msg.type}-${index}`} className={`message ${msg.type}`}>
              {msg.type === 'user' ? (
                // User message with edit functionality
                <div className="user-message">
                  <div className="user-content">
                    {editingMessageId === msg.id ? (
                      // Edit mode
                      <div className="user-edit-container">
                        <textarea
                          className="user-edit-textarea"
                          value={editingText}
                          onChange={(e) => {
                            setEditingText(e.target.value);
                            // Auto-resize textarea to fit content
                            e.target.style.height = 'auto';
                            e.target.style.height = e.target.scrollHeight + 'px';
                          }}
                          autoFocus
                          onFocus={(e) => {
                            // Set initial height when focused
                            e.target.style.height = 'auto';
                            e.target.style.height = e.target.scrollHeight + 'px';
                          }}
                        />
                        <div className="user-edit-actions">
                          <button
                            className="edit-action-btn cancel-btn"
                            onClick={handleCancelEdit}
                          >
                            Cancel
                          </button>
                          <button
                            className="edit-action-btn save-btn"
                            onClick={handleSaveEdit}
                          >
                            Send
                          </button>
                        </div>
                      </div>
                    ) : (
                      // Display mode
                      <div className="user-display-container">
                        <div className="user-text">{msg.text}</div>
                        <div className="user-message-actions">
                          <button
                            className="user-action-btn"
                            onClick={() => handleCopyMessage(msg.text)}
                            title="Copy message"
                          >
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                            </svg>
                          </button>
                          <button
                            className="user-action-btn"
                            onClick={() => handleEditMessage(msg.id, msg.text)}
                            title="Edit message"
                          >
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                            </svg>
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                // Simple assistant message display
                <div className="assistant-message">
                  <div className="assistant-content">
                    {msg.isStructured ? (
                      <ResponseRenderer
                        response={msg.text}
                        userMessage={msg.originalQuery || ''}
                      />
                    ) : (msg.downloadInfo && msg.downloadInfo.downloadUrl) ? (
                      <div>
                        <div className="assistant-text">
                          {typeof msg.text === 'string' ? msg.text : JSON.stringify(msg.text)}
                        </div>
                        {console.log('Rendering download info:', msg.downloadInfo)}
                        <DownloadRenderer
                          downloadInfo={msg.downloadInfo}
                          onDownload={(info) => console.log('Downloaded:', info)}
                        />
                      </div>
                    ) : (
                      <div className="assistant-text">
                        {typeof msg.text === 'string' ? msg.text : JSON.stringify(msg.text)}
                      </div>
                    )}

                    {/* Message Actions - SVG Icons */}
                    <div className="message-actions">
                      <div className="action-buttons">
                        <button
                          className="action-btn"
                          onClick={() => handleCopyMessage(msg.text)}
                          title="Copy message"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                          </svg>
                        </button>
                        <button
                          className="action-btn"
                          onClick={() => handleThumbsUp(msg.id)}
                          title="Good response"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                            <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>
                          </svg>
                        </button>
                        <button
                          className="action-btn"
                          onClick={() => handleThumbsDown(msg.id)}
                          title="Poor response"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                            <path d="M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h2.67A2.31 2.31 0 0 1 22 4v7a2.31 2.31 0 0 1-2.33 2H17"></path>
                          </svg>
                        </button>
                        <button
                          className="action-btn"
                          onClick={() => handleReadAloud(msg.text)}
                          title="Read aloud"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                            <polygon points="11 5,6 9,2 9,2 15,6 15,11 19,11 5"></polygon>
                            <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path>
                          </svg>
                        </button>
                        <button
                          className="action-btn"
                          onClick={() => handleEmailReport(msg)}
                          title="Email this report"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                            <rect x="2" y="4" width="20" height="16" rx="2"></rect>
                            <path d="m22 7-10 5L2 7"></path>
                          </svg>
                        </button>
                        {msg.downloadInfo && (
                          <button
                            className="action-btn"
                            onClick={() => window.open(msg.downloadInfo.downloadUrl)}
                            title="Download"
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                              <polyline points="7,10 12,15 17,10"></polyline>
                              <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
            );
          })}

          {/* Waiting Message Component */}
          {waitingState.isWaiting && (
            <div className={`message assistant ${postResponseWait ? 'post-response' : ''}`}>
              <div className="assistant-message">
                <div className="assistant-content">
                  <WaitingMessage
                    isVisible={waitingState.isWaiting}
                    currentStep={waitingState.currentStep}
                    steps={waitingState.steps}
                    autoProgress={false}
                    dynamicMessage={dynamicMessage}
                    isProcessing={isProcessingResponse}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Legacy loading indicator (fallback) */}
          {isLoading && !waitingState.isWaiting && (
            <div className="message assistant">
              <div className="assistant-message">
                <div className="assistant-content">
                  <div className="typing-indicator">
                    <div className="typing-dots">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                    <span className="typing-text">Processing your request...</span>
                  </div>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      )}

      {/* Input area only visible when there are messages */}
      {hasMessages && (
        <div className="input-area">
          <div className="input-container">
            <div className="input-wrapper">
              <div className="left-actions">
                <button className="add-btn" title="Add">
                  +
                </button>
                <button className="tools-btn" title="Tools">
                  <span className="tools-icon">⚙</span>
                  <span>Tools</span>
                </button>
              </div>
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Ask anything"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSend();
                  }
                }}
                className="chat-input"
                rows="1"
              />
              <div className="right-actions">
                <button
                  className={`action-btn voice-btn ${isListening ? 'listening' : ''}`}
                  onClick={isListening ? stopListening : startListening}
                  title={isListening ? 'Stop listening' : 'Start voice input'}
                >
                  🎤
                </button>
                <button
                  className="action-btn send-btn"
                  onClick={handleSend}
                  disabled={!message.trim()}
                  title="Send message"
                >
                  ↗
                </button>
              </div>
            </div>
          </div>

          {/* ChatGPT-style disclaimer */}
          <div className="disclaimer">
            <p>ReportDesk can make mistakes. Check important info.</p>
          </div>
        </div>
      )}

      {/* Email Report Modal */}
      <EmailReportModal
        isOpen={emailModalOpen}
        onClose={() => setEmailModalOpen(false)}
        message={selectedMessage}
        userEmail={currentUser.email}
        userName={currentUser.name}
      />
    </div>
  );
}

