"""
Dynamic Schema Discovery Module

This module automatically discovers database schema information
without requiring manual configuration of every table.
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
from loguru import logger

try:
    from sqlalchemy import text, inspect
    from sqlalchemy.ext.asyncio import AsyncSession
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False


@dataclass
class ColumnInfo:
    """Information about a database column"""
    name: str
    type: str
    nullable: bool
    default: Optional[str] = None
    primary_key: bool = False
    foreign_key: Optional[str] = None
    description: Optional[str] = None


@dataclass
class TableInfo:
    """Information about a database table"""
    name: str
    schema: str
    columns: Dict[str, ColumnInfo]
    row_count: Optional[int] = None
    description: Optional[str] = None
    table_type: str = "table"  # table, view, etc.
    business_keywords: List[str] = None
    
    def __post_init__(self):
        if self.business_keywords is None:
            self.business_keywords = []


@dataclass
class SchemaCache:
    """Cache for discovered schema information"""
    datasource: str
    tables: Dict[str, TableInfo]
    discovered_at: datetime
    expires_at: datetime


class DynamicSchemaDiscovery:
    """
    Discovers database schema dynamically without manual configuration
    """
    
    def __init__(self, connection_manager, config_manager):
        """
        Initialize schema discovery
        
        Args:
            connection_manager: Database connection manager
            config_manager: Configuration manager
        """
        self.connection_manager = connection_manager
        self.config_manager = config_manager
        self.schema_cache: Dict[str, SchemaCache] = {}
        
        # Business context patterns for auto-classification
        self.business_patterns = self._load_business_patterns()
        
        logger.info("Dynamic schema discovery initialized")

    def _load_business_patterns(self) -> Dict[str, List[str]]:
        """Load business patterns from configuration or use defaults"""
        # Load from configuration if available, otherwise use defaults
        return {
            'sales': ['sale', 'order', 'customer', 'product', 'revenue', 'invoice', 'payment'],
            'hr': ['employee', 'staff', 'payroll', 'department', 'position', 'salary'],
            'finance': ['account', 'transaction', 'budget', 'expense', 'cost', 'profit'],
            'inventory': ['stock', 'warehouse', 'supplier', 'purchase', 'inventory'],
            'marketing': ['campaign', 'lead', 'prospect', 'conversion', 'analytics'],
            'tax': ['tax', 'code', 'rate', 'gst', 'vat', 'einvoicing']
        }
    
    async def discover_schema(self, datasource_name: str, force_refresh: bool = False) -> Dict[str, TableInfo]:
        """
        Discover schema for a data source
        
        Args:
            datasource_name: Name of the data source
            force_refresh: Force refresh even if cached
            
        Returns:
            Dictionary of table information
        """
        try:
            # Check cache first
            if not force_refresh and datasource_name in self.schema_cache:
                cache_entry = self.schema_cache[datasource_name]
                if datetime.now() < cache_entry.expires_at:
                    logger.info(f"Using cached schema for {datasource_name}")
                    return cache_entry.tables
            
            logger.info(f"Discovering schema for {datasource_name}")
            
            # Get data source configuration
            datasource = self.config_manager.get_data_source(datasource_name)
            if not datasource:
                logger.error(f"Data source not found: {datasource_name}")
                return {}
            
            # Check for Excel connection first
            excel_connection = self.connection_manager.excel_connections.get(datasource_name)
            if excel_connection:
                tables = await self._discover_excel_schema(excel_connection, datasource)
            else:
                # Get SQL connection
                connection_info = self.connection_manager.connections.get(datasource_name)
                if not connection_info:
                    logger.error(f"No connection available for {datasource_name}")
                    return {}

                # Discover tables based on database type
                if datasource.type.lower() == 'sqlserver':
                    tables = await self._discover_sqlserver_schema(connection_info, datasource)
                elif datasource.type.lower() == 'postgresql':
                    tables = await self._discover_postgresql_schema(connection_info, datasource)
                elif datasource.type.lower() == 'mysql':
                    tables = await self._discover_mysql_schema(connection_info, datasource)
                elif datasource.type.lower() == 'oracle':
                    tables = await self._discover_oracle_schema(connection_info, datasource)
                else:
                    tables = await self._discover_generic_schema(connection_info, datasource)
            
            # Add business context
            tables = self._add_business_context(tables, datasource)
            
            # Cache the results
            cache_ttl = 3600  # Default 1 hour cache
            self.schema_cache[datasource_name] = SchemaCache(
                datasource=datasource_name,
                tables=tables,
                discovered_at=datetime.now(),
                expires_at=datetime.now() + timedelta(seconds=cache_ttl)
            )
            
            logger.info(f"Discovered {len(tables)} tables for {datasource_name}")
            return tables
            
        except Exception as e:
            logger.error(f"Error discovering schema for {datasource_name}: {e}")
            return {}
    
    async def _discover_sqlserver_schema(self, connection_info, datasource) -> Dict[str, TableInfo]:
        """Discover SQL Server schema"""
        tables = {}
        
        async with connection_info.session_maker() as session:
            # Get auto-discovery settings
            auto_config = datasource.connection.get('auto_discovery', {})
            include_schemas = auto_config.get('include_schemas', ['dbo'])
            exclude_tables = auto_config.get('exclude_tables', [])
            include_views = auto_config.get('include_views', True)
            
            # Query to get tables and views
            schema_filter = "', '".join(include_schemas)
            table_query = f"""
            SELECT 
                t.TABLE_SCHEMA,
                t.TABLE_NAME,
                t.TABLE_TYPE,
                ISNULL(ep.value, '') as TABLE_DESCRIPTION
            FROM INFORMATION_SCHEMA.TABLES t
            LEFT JOIN sys.extended_properties ep ON ep.major_id = OBJECT_ID(t.TABLE_SCHEMA + '.' + t.TABLE_NAME)
                AND ep.minor_id = 0 AND ep.name = 'MS_Description'
            WHERE t.TABLE_SCHEMA IN ('{schema_filter}')
            """
            
            if not include_views:
                table_query += " AND t.TABLE_TYPE = 'BASE TABLE'"
            
            result = await session.execute(text(table_query))
            table_rows = result.fetchall()
            
            for row in table_rows:
                schema_name, table_name, table_type, description = row
                full_table_name = f"{schema_name}.{table_name}"
                
                # Skip excluded tables
                if any(exclude in table_name.lower() for exclude in exclude_tables):
                    continue
                
                # Get column information
                columns = await self._get_sqlserver_columns(session, schema_name, table_name)
                
                tables[full_table_name] = TableInfo(
                    name=table_name,
                    schema=schema_name,
                    columns=columns,
                    description=description or None,
                    table_type=table_type.lower()
                )
        
        return tables
    
    async def _get_sqlserver_columns(self, session: AsyncSession, schema: str, table: str) -> Dict[str, ColumnInfo]:
        """Get column information for SQL Server table"""
        columns = {}
        
        column_query = f"""
        SELECT 
            c.COLUMN_NAME,
            c.DATA_TYPE,
            c.IS_NULLABLE,
            c.COLUMN_DEFAULT,
            CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END as IS_PRIMARY_KEY,
            ISNULL(ep.value, '') as COLUMN_DESCRIPTION
        FROM INFORMATION_SCHEMA.COLUMNS c
        LEFT JOIN (
            SELECT ku.TABLE_SCHEMA, ku.TABLE_NAME, ku.COLUMN_NAME
            FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
            JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
            WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
        ) pk ON c.TABLE_SCHEMA = pk.TABLE_SCHEMA AND c.TABLE_NAME = pk.TABLE_NAME AND c.COLUMN_NAME = pk.COLUMN_NAME
        LEFT JOIN sys.extended_properties ep ON ep.major_id = OBJECT_ID(c.TABLE_SCHEMA + '.' + c.TABLE_NAME)
            AND ep.minor_id = COLUMNPROPERTY(OBJECT_ID(c.TABLE_SCHEMA + '.' + c.TABLE_NAME), c.COLUMN_NAME, 'ColumnId')
            AND ep.name = 'MS_Description'
        WHERE c.TABLE_SCHEMA = '{schema}' AND c.TABLE_NAME = '{table}'
        ORDER BY c.ORDINAL_POSITION
        """
        
        result = await session.execute(text(column_query))
        column_rows = result.fetchall()
        
        for row in column_rows:
            col_name, data_type, is_nullable, default_val, is_pk, description = row
            
            columns[col_name] = ColumnInfo(
                name=col_name,
                type=data_type,
                nullable=is_nullable == 'YES',
                default=default_val,
                primary_key=bool(is_pk),
                description=description or None
            )
        
        return columns

    async def _get_oracle_columns(self, session: AsyncSession, schema: str, table: str) -> Dict[str, ColumnInfo]:
        """Get column information for an Oracle table"""
        columns = {}

        column_query = f"""
        SELECT
            c.COLUMN_NAME,
            c.DATA_TYPE ||
            CASE
                WHEN c.DATA_TYPE IN ('VARCHAR2', 'CHAR', 'NVARCHAR2', 'NCHAR') THEN '(' || c.DATA_LENGTH || ')'
                WHEN c.DATA_TYPE IN ('NUMBER') AND c.DATA_PRECISION IS NOT NULL THEN
                    '(' || c.DATA_PRECISION ||
                    CASE WHEN c.DATA_SCALE > 0 THEN ',' || c.DATA_SCALE ELSE '' END || ')'
                ELSE ''
            END as DATA_TYPE_FULL,
            c.NULLABLE,
            c.DATA_DEFAULT,
            CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END as IS_PRIMARY_KEY,
            NVL(cc.COMMENTS, '') as COLUMN_DESCRIPTION
        FROM ALL_TAB_COLUMNS c
        LEFT JOIN (
            SELECT acc.OWNER, acc.TABLE_NAME, acc.COLUMN_NAME
            FROM ALL_CONSTRAINTS ac
            JOIN ALL_CONS_COLUMNS acc ON ac.CONSTRAINT_NAME = acc.CONSTRAINT_NAME
                AND ac.OWNER = acc.OWNER
            WHERE ac.CONSTRAINT_TYPE = 'P'
        ) pk ON c.OWNER = pk.OWNER AND c.TABLE_NAME = pk.TABLE_NAME AND c.COLUMN_NAME = pk.COLUMN_NAME
        LEFT JOIN ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME
            AND c.COLUMN_NAME = cc.COLUMN_NAME
        WHERE c.OWNER = '{schema}' AND c.TABLE_NAME = '{table}'
        ORDER BY c.COLUMN_ID
        """

        result = await session.execute(text(column_query))
        column_rows = result.fetchall()

        for row in column_rows:
            col_name, data_type, is_nullable, default_val, is_pk, description = row

            columns[col_name] = ColumnInfo(
                name=col_name,
                type=data_type,
                nullable=is_nullable == 'Y',
                default=default_val,
                primary_key=bool(is_pk),
                description=description or None
            )

        return columns
    
    async def _discover_postgresql_schema(self, connection_info, datasource) -> Dict[str, TableInfo]:
        """Discover PostgreSQL schema"""
        # Similar implementation for PostgreSQL
        # Using information_schema.tables and information_schema.columns
        return {}
    
    async def _discover_mysql_schema(self, connection_info, datasource) -> Dict[str, TableInfo]:
        """Discover MySQL schema"""
        # Similar implementation for MySQL
        return {}

    async def _discover_oracle_schema(self, connection_info, datasource) -> Dict[str, TableInfo]:
        """Discover Oracle schema"""
        tables = {}

        async with connection_info.session_maker() as session:
            # Get auto-discovery settings
            auto_config = datasource.connection.get('auto_discovery', {})
            include_schemas = auto_config.get('include_schemas', ['HR'])  # Default to HR schema
            exclude_tables = auto_config.get('exclude_tables', [])
            include_views = auto_config.get('include_views', True)

            # Query to get tables and views from Oracle
            schema_filter = "', '".join(include_schemas)
            table_query = f"""
            SELECT
                t.OWNER as TABLE_SCHEMA,
                t.TABLE_NAME,
                CASE WHEN v.VIEW_NAME IS NOT NULL THEN 'VIEW' ELSE 'BASE TABLE' END as TABLE_TYPE,
                NVL(c.COMMENTS, '') as TABLE_DESCRIPTION
            FROM ALL_TABLES t
            LEFT JOIN ALL_TAB_COMMENTS c ON t.OWNER = c.OWNER AND t.TABLE_NAME = c.TABLE_NAME
            LEFT JOIN ALL_VIEWS v ON t.OWNER = v.OWNER AND t.TABLE_NAME = v.VIEW_NAME
            WHERE t.OWNER IN ('{schema_filter}')
            """

            if not include_views:
                table_query += " AND v.VIEW_NAME IS NULL"

            result = await session.execute(text(table_query))
            table_rows = result.fetchall()

            for row in table_rows:
                schema_name, table_name, table_type, description = row
                full_table_name = f"{schema_name}.{table_name}"

                # Skip excluded tables
                if any(exclude in table_name.lower() for exclude in exclude_tables):
                    continue

                # Get column information
                columns = await self._get_oracle_columns(session, schema_name, table_name)

                tables[full_table_name] = TableInfo(
                    name=table_name,
                    schema=schema_name,
                    columns=columns,
                    description=description or None,
                    table_type=table_type.lower()
                )

        return tables
    
    async def _discover_generic_schema(self, connection_info, datasource) -> Dict[str, TableInfo]:
        """Generic schema discovery using SQLAlchemy inspector"""
        tables = {}

        try:
            async with connection_info.session_maker() as session:
                # Use SQLAlchemy inspector for generic discovery
                inspector = inspect(connection_info.engine.sync_engine)

                for table_name in inspector.get_table_names():
                    columns = {}

                    for column in inspector.get_columns(table_name):
                        columns[column['name']] = ColumnInfo(
                            name=column['name'],
                            type=str(column['type']),
                            nullable=column.get('nullable', True),
                            default=column.get('default'),
                            primary_key=column.get('primary_key', False)
                        )

                    tables[table_name] = TableInfo(
                        name=table_name,
                        schema='public',
                        columns=columns
                    )

        except Exception as e:
            logger.error(f"Error in generic schema discovery: {e}")

        return tables

    async def _discover_excel_schema(self, excel_connection, datasource) -> Dict[str, TableInfo]:
        """Discover Excel file schema from DataFrame"""
        tables = {}

        try:
            # Get the DataFrame
            df = excel_connection.dataframe
            sheet_name = excel_connection.sheet_name

            # Create columns info from DataFrame
            columns = {}
            for col_name in df.columns:
                # Determine data type from pandas dtype
                dtype = df[col_name].dtype

                if dtype == 'object':
                    sql_type = 'VARCHAR(255)'
                elif dtype == 'int64':
                    sql_type = 'INTEGER'
                elif dtype == 'float64':
                    sql_type = 'DECIMAL'
                elif dtype == 'bool':
                    sql_type = 'BOOLEAN'
                elif 'datetime' in str(dtype):
                    sql_type = 'DATETIME'
                else:
                    sql_type = 'VARCHAR(255)'

                # Check for null values
                has_nulls = df[col_name].isnull().any()

                columns[col_name] = ColumnInfo(
                    name=col_name,
                    type=sql_type,
                    nullable=has_nulls,
                    description=f"Column from Excel sheet: {sheet_name}"
                )

            # Create table info for the Excel sheet
            tables[sheet_name] = TableInfo(
                name=sheet_name,
                schema='excel',
                columns=columns,
                row_count=len(df),
                description=f"Excel sheet: {sheet_name} from {excel_connection.file_path}",
                table_type="excel_sheet",
                business_keywords=datasource.connection.get('business_context', {}).get('keywords', [])
            )

            logger.info(f"Discovered Excel schema: {sheet_name} with {len(columns)} columns and {len(df)} rows")

        except Exception as e:
            logger.error(f"Error discovering Excel schema: {e}")

        return tables
    
    def _add_business_context(self, tables: Dict[str, TableInfo], datasource) -> Dict[str, TableInfo]:
        """Add business context and keywords to discovered tables"""
        
        # Get business context from configuration
        business_config = getattr(datasource, 'business_context', {})
        domain = business_config.get('domain', 'general')
        configured_keywords = business_config.get('keywords', [])
        
        for table_name, table_info in tables.items():
            # Generate keywords based on table and column names
            keywords = set(configured_keywords)
            
            # Add table name variations
            keywords.add(table_info.name.lower())
            keywords.update(table_info.name.lower().split('_'))
            
            # Add column name keywords
            for column_name in table_info.columns.keys():
                keywords.update(column_name.lower().split('_'))
            
            # Add domain-specific keywords
            if domain in self.business_patterns:
                domain_keywords = self.business_patterns[domain]
                for keyword in domain_keywords:
                    if keyword in table_info.name.lower():
                        keywords.update(domain_keywords)
                        break
            
            table_info.business_keywords = list(keywords)
        
        return tables
    
    def find_relevant_tables(self, query_text: str, datasource_name: str) -> List[str]:
        """
        Find relevant tables based on query text using discovered schema
        
        Args:
            query_text: Natural language query
            datasource_name: Data source name
            
        Returns:
            List of relevant table names
        """
        if datasource_name not in self.schema_cache:
            return []
        
        query_lower = query_text.lower()
        relevant_tables = []
        tables = self.schema_cache[datasource_name].tables
        
        # Score tables based on keyword matches
        table_scores = {}
        
        for table_name, table_info in tables.items():
            score = 0
            
            # Check table name match
            if table_info.name.lower() in query_lower:
                score += 10
            
            # Check keyword matches
            for keyword in table_info.business_keywords:
                if keyword in query_lower:
                    score += 1
            
            # Check column name matches
            for column_name in table_info.columns.keys():
                if column_name.lower() in query_lower:
                    score += 2
            
            if score > 0:
                table_scores[table_name] = score
        
        # Return tables sorted by relevance score
        sorted_tables = sorted(table_scores.items(), key=lambda x: x[1], reverse=True)
        return [table_name for table_name, score in sorted_tables[:5]]  # Top 5 relevant tables
    
    def get_table_info(self, datasource_name: str, table_name: str) -> Optional[TableInfo]:
        """Get information about a specific table"""
        if datasource_name not in self.schema_cache:
            return None
        
        tables = self.schema_cache[datasource_name].tables
        return tables.get(table_name)
    
    def clear_cache(self, datasource_name: Optional[str] = None):
        """Clear schema cache"""
        if datasource_name:
            self.schema_cache.pop(datasource_name, None)
        else:
            self.schema_cache.clear()
        
        logger.info(f"Schema cache cleared for {datasource_name or 'all datasources'}")
