/**
 * Power BI Service for generating and embedding Power BI reports
 */

class PowerBIService {
  constructor() {
    this.baseUrl = 'https://app.powerbi.com';
    this.embedUrl = 'https://app.powerbi.com/reportEmbed';
    
    // Sample report configurations
    this.reportConfigs = {
      sales: {
        reportId: 'sales-dashboard-001',
        groupId: 'sample-workspace',
        title: 'Sales Performance Dashboard'
      },
      financial: {
        reportId: 'financial-report-001', 
        groupId: 'sample-workspace',
        title: 'Financial Analysis Report'
      },
      operations: {
        reportId: 'operations-dashboard-001',
        groupId: 'sample-workspace', 
        title: 'Operations Dashboard'
      },
      default: {
        reportId: 'general-report-001',
        groupId: 'sample-workspace',
        title: 'General Analytics Report'
      }
    };
  }

  /**
   * Determine report type based on message content
   */
  determineReportType(message) {
    const text = (message.originalQuery || message.text || '').toLowerCase();
    
    if (text.includes('sales') || text.includes('revenue') || text.includes('customer')) {
      return 'sales';
    } else if (text.includes('financial') || text.includes('profit') || text.includes('expense')) {
      return 'financial';
    } else if (text.includes('operations') || text.includes('production') || text.includes('efficiency')) {
      return 'operations';
    }
    
    return 'default';
  }

  /**
   * Generate Power BI report URL with filters
   */
  generateReportUrl(message) {
    const reportType = this.determineReportType(message);
    const config = this.reportConfigs[reportType];
    
    // Create filter parameters based on message data
    const filters = this.createFilters(message);
    const filterString = filters.length > 0 ? `&$filter=${encodeURIComponent(filters.join(' and '))}` : '';
    
    // Construct the embed URL
    const embedUrl = `${this.embedUrl}?reportId=${config.reportId}&groupId=${config.groupId}${filterString}`;
    
    return {
      url: embedUrl,
      title: config.title,
      reportType: reportType,
      config: config
    };
  }

  /**
   * Create filters based on message content and data
   */
  createFilters(message) {
    const filters = [];
    
    // Add timestamp filter
    const date = new Date().toISOString().split('T')[0];
    filters.push(`Date ge datetime'${date}'`);
    
    // Add query-based filters
    const query = (message.originalQuery || message.text || '').toLowerCase();
    
    if (query.includes('last month')) {
      const lastMonth = new Date();
      lastMonth.setMonth(lastMonth.getMonth() - 1);
      filters.push(`Date ge datetime'${lastMonth.toISOString().split('T')[0]}'`);
    }
    
    if (query.includes('this year')) {
      const thisYear = new Date().getFullYear();
      filters.push(`Year eq ${thisYear}`);
    }
    
    // Add data-based filters if downloadInfo exists
    if (message.downloadInfo && message.downloadInfo.metadata) {
      const metadata = message.downloadInfo.metadata;
      if (metadata.department) {
        filters.push(`Department eq '${metadata.department}'`);
      }
      if (metadata.region) {
        filters.push(`Region eq '${metadata.region}'`);
      }
    }
    
    return filters;
  }

  /**
   * Open Power BI report in new window
   */
  async openReport(message) {
    try {
      const reportInfo = this.generateReportUrl(message);
      
      // Log the report generation
      console.log('Opening Power BI report:', {
        type: reportInfo.reportType,
        title: reportInfo.title,
        messageId: message.id
      });
      
      // Open in new window with specific dimensions
      const windowFeatures = [
        'width=1400',
        'height=900',
        'scrollbars=yes',
        'resizable=yes',
        'toolbar=no',
        'menubar=no',
        'location=no',
        'status=no'
      ].join(',');
      
      const powerBIWindow = window.open(reportInfo.url, '_blank', windowFeatures);
      
      if (powerBIWindow) {
        // Set window title
        powerBIWindow.document.title = reportInfo.title;
        
        // Focus the new window
        powerBIWindow.focus();
        
        return {
          success: true,
          reportInfo: reportInfo,
          window: powerBIWindow
        };
      } else {
        throw new Error('Failed to open Power BI window. Please check your popup blocker settings.');
      }
      
    } catch (error) {
      console.error('Error opening Power BI report:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create a demo Power BI report URL for testing
   */
  createDemoReport(message) {
    const reportType = this.determineReportType(message);
    
    // Create a demo URL that shows a sample Power BI dashboard
    const demoUrl = `https://app.powerbi.com/view?r=eyJrIjoiYjU5ZTZiYWQtNWI4OS00YjZkLWE4YjYtMjg5ZGQwYzU5YzU5IiwidCI6IjcyZjk4OGJmLTg2ZjEtNDFhZi05MWFiLTJkN2NkMDExZGI0NyIsImMiOjV9&reportType=${reportType}&query=${encodeURIComponent(message.originalQuery || message.text)}`;
    
    return {
      url: demoUrl,
      title: `${this.reportConfigs[reportType].title} - Demo`,
      reportType: reportType,
      isDemo: true
    };
  }

  /**
   * Get available report types
   */
  getAvailableReports() {
    return Object.keys(this.reportConfigs).map(key => ({
      type: key,
      title: this.reportConfigs[key].title,
      reportId: this.reportConfigs[key].reportId
    }));
  }
}

// Export singleton instance
const powerBIService = new PowerBIService();
export default powerBIService;
