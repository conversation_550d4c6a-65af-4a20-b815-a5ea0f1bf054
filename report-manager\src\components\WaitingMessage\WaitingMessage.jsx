import React, { useEffect, useState } from 'react';
import './WaitingMessage.css';

const WaitingMessage = ({
  isVisible,
  currentStep,
  steps,
  onStepComplete,
  autoProgress = true,
  stepDuration = 2000,
  dynamicMessage = '',
  isProcessing = false
}) => {
  const [displayedStep, setDisplayedStep] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (!isVisible || !autoProgress) return;

    const timer = setTimeout(() => {
      if (displayedStep < steps.length - 1) {
        setIsAnimating(true);
        setTimeout(() => {
          setDisplayedStep(prev => prev + 1);
          setIsAnimating(false);
          if (onStepComplete) {
            onStepComplete(displayedStep + 1);
          }
        }, 300);
      }
    }, stepDuration);

    return () => clearTimeout(timer);
  }, [displayedStep, isVisible, autoProgress, stepDuration, steps.length, onStepComplete]);

  useEffect(() => {
    if (currentStep !== undefined && currentStep !== displayedStep) {
      setDisplayedStep(currentStep);
    }
  }, [currentStep]);

  useEffect(() => {
    if (isVisible) {
      setDisplayedStep(0);
      setIsAnimating(false);
    }
  }, [isVisible]);

  if (!isVisible) return null;

  const currentStepData = steps[displayedStep] || steps[0];

  return (
    <div className="waiting-message">
      <div className={`waiting-content ${isProcessing ? 'processing' : ''}`}>
        <div className={`waiting-step ${isAnimating ? 'animating' : ''}`}>
          <div className="step-icon">
            {currentStepData.icon}
          </div>
          <div className="step-text">
            {dynamicMessage || currentStepData.message}
          </div>
        </div>

        <div className="waiting-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </div>
  );
};

export default WaitingMessage;
