import React, { useState } from 'react';
import MetricsDisplay from './MetricsDisplay';
import ReportsDisplay from './ReportsDisplay';
import ContextDisplay from './ContextDisplay';
import AgentsDisplay from './AgentsDisplay';
import ReportGenerator from '../ReportGenerator/ReportGenerator';
import './ResponseRenderer.css';

const ResponseRenderer = ({ response, className = '', userMessage = '' }) => {
  const [showReportGenerator, setShowReportGenerator] = useState(false);

  console.log('ResponseRenderer received:', response);

  if (!response || typeof response !== 'object') {
    return <div className="response-text">{String(response || '')}</div>;
  }

  // If it's a simple string response
  if (typeof response === 'string') {
    return <div className="response-text">{response}</div>;
  }

  // Check if this is a structured API response
  const hasStructuredData = response.result && response.result.data;

  console.log('hasStructuredData:', hasStructuredData);
  console.log('response.result:', response.result);

  if (!hasStructuredData) {
    // Handle simple object responses
    console.log('Rendering as JSON fallback');
    return <div className="response-text">{JSON.stringify(response, null, 2)}</div>;
  }

  const { result, context, agents_used, execution_time, query_type } = response;
  const { data } = result;

  console.log('Structured data found:', { data, context, agents_used });

  return (
    <div className={`response-renderer ${className}`}>
      {/* Main Data Display */}
      <div className="response-content">
        {data.metrics && data.metrics.length > 0 && (
          <MetricsDisplay metrics={data.metrics} />
        )}
        
        {data.reports && data.reports.length > 0 && (
          <ReportsDisplay reports={data.reports} />
        )}
      </div>

      {/* Report Generation Section */}
      <div className="response-actions">
        <button
          className="generate-report-btn"
          onClick={() => setShowReportGenerator(!showReportGenerator)}
        >
          📊 {showReportGenerator ? 'Hide' : 'Generate'} Downloadable Report
        </button>
      </div>

      {showReportGenerator && (
        <div className="report-generator-section">
          <ReportGenerator
            userMessage={userMessage}
            onReportGenerated={(result) => {
              console.log('Report generated:', result);
              // You can add additional handling here
            }}
          />
        </div>
      )}

      {/* Metadata Section */}
      <div className="response-metadata">
        {context && (
          <ContextDisplay context={context} />
        )}

        {agents_used && agents_used.length > 0 && (
          <AgentsDisplay
            agents={agents_used}
            executionTime={execution_time}
            queryType={query_type}
          />
        )}
      </div>
    </div>
  );
};

export default ResponseRenderer;
