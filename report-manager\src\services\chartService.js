/**
 * Chart Service for generating interactive charts from message data
 */

class ChartService {
  constructor() {
    this.chartTypes = {
      bar: 'Bar Chart',
      line: 'Line Chart', 
      pie: 'Pie Chart',
      area: 'Area Chart',
      scatter: 'Scatter Plot',
      column: 'Column Chart'
    };
    
    this.chartLibraries = {
      chartjs: 'Chart.js',
      plotly: 'Plotly.js',
      d3: 'D3.js',
      highcharts: 'Highcharts'
    };
  }

  /**
   * Determine best chart type based on message content
   */
  determineChartType(message) {
    const text = (message.originalQuery || message.text || '').toLowerCase();
    
    if (text.includes('trend') || text.includes('over time') || text.includes('timeline')) {
      return 'line';
    } else if (text.includes('comparison') || text.includes('compare') || text.includes('vs')) {
      return 'bar';
    } else if (text.includes('distribution') || text.includes('percentage') || text.includes('share')) {
      return 'pie';
    } else if (text.includes('correlation') || text.includes('relationship')) {
      return 'scatter';
    } else if (text.includes('growth') || text.includes('increase') || text.includes('decrease')) {
      return 'area';
    }
    
    return 'column'; // Default
  }

  /**
   * Extract data from message for chart generation
   */
  extractChartData(message) {
    const chartData = {
      labels: [],
      datasets: [],
      title: '',
      xAxisLabel: '',
      yAxisLabel: ''
    };

    // Try to extract data from downloadInfo
    if (message.downloadInfo && message.downloadInfo.metadata) {
      const metadata = message.downloadInfo.metadata;
      chartData.title = metadata.title || 'Generated Chart';
      
      // Sample data extraction logic
      if (metadata.sheets) {
        const firstSheet = Object.values(metadata.sheets)[0];
        if (firstSheet && firstSheet.headers) {
          chartData.labels = firstSheet.headers.slice(1); // Skip first column as labels
          chartData.xAxisLabel = firstSheet.headers[0] || 'Category';
          chartData.yAxisLabel = 'Value';
        }
      }
    }

    // Generate sample data if no real data available
    if (chartData.labels.length === 0) {
      chartData.labels = this.generateSampleLabels(message);
      chartData.datasets = this.generateSampleDatasets(message, chartData.labels);
      chartData.title = this.generateChartTitle(message);
    }

    return chartData;
  }

  /**
   * Generate sample labels based on message content
   */
  generateSampleLabels(message) {
    const text = (message.originalQuery || message.text || '').toLowerCase();
    
    if (text.includes('month') || text.includes('monthly')) {
      return ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    } else if (text.includes('quarter') || text.includes('quarterly')) {
      return ['Q1', 'Q2', 'Q3', 'Q4'];
    } else if (text.includes('year') || text.includes('annual')) {
      return ['2020', '2021', '2022', '2023', '2024'];
    } else if (text.includes('department') || text.includes('team')) {
      return ['Sales', 'Marketing', 'Operations', 'Finance', 'HR'];
    } else if (text.includes('region') || text.includes('location')) {
      return ['North', 'South', 'East', 'West', 'Central'];
    }
    
    return ['Category A', 'Category B', 'Category C', 'Category D', 'Category E'];
  }

  /**
   * Generate sample datasets
   */
  generateSampleDatasets(message, labels) {
    const text = (message.originalQuery || message.text || '').toLowerCase();
    const datasetCount = text.includes('compare') || text.includes('vs') ? 2 : 1;
    const datasets = [];

    for (let i = 0; i < datasetCount; i++) {
      const data = labels.map(() => Math.floor(Math.random() * 100) + 10);
      const colors = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6'];
      
      datasets.push({
        label: datasetCount > 1 ? `Series ${i + 1}` : 'Data',
        data: data,
        backgroundColor: colors[i % colors.length],
        borderColor: colors[i % colors.length],
        borderWidth: 2,
        fill: false
      });
    }

    return datasets;
  }

  /**
   * Generate chart title based on message
   */
  generateChartTitle(message) {
    const text = message.originalQuery || message.text || '';
    
    if (text.length > 50) {
      return text.substring(0, 47) + '...';
    }
    
    return text || 'Data Visualization';
  }

  /**
   * Create chart configuration
   */
  createChartConfig(message) {
    const chartType = this.determineChartType(message);
    const chartData = this.extractChartData(message);
    
    const config = {
      type: chartType,
      data: chartData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: chartData.title,
            font: {
              size: 16,
              weight: 'bold'
            }
          },
          legend: {
            display: chartData.datasets.length > 1,
            position: 'top'
          }
        },
        scales: {
          x: {
            title: {
              display: true,
              text: chartData.xAxisLabel
            }
          },
          y: {
            title: {
              display: true,
              text: chartData.yAxisLabel
            },
            beginAtZero: true
          }
        }
      }
    };

    return config;
  }

  /**
   * Generate chart HTML page
   */
  generateChartHTML(message) {
    const config = this.createChartConfig(message);
    const chartType = this.determineChartType(message);
    
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${config.data.title}</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        .chart-container {
            position: relative;
            height: 500px;
            margin: 20px 0;
        }
        .info {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            padding-top: 16px;
            border-top: 1px solid #e5e7eb;
            font-size: 14px;
            color: #6b7280;
        }
        .chart-type {
            background: #3b82f6;
            color: white;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${config.data.title}</h1>
            <span class="chart-type">${this.chartTypes[chartType]}</span>
        </div>
        <div class="chart-container">
            <canvas id="chart"></canvas>
        </div>
        <div class="info">
            <span>Generated from: ${message.originalQuery || message.text}</span>
            <span>Created: ${new Date().toLocaleString()}</span>
        </div>
    </div>

    <script>
        const ctx = document.getElementById('chart').getContext('2d');
        const chart = new Chart(ctx, ${JSON.stringify(config)});
        
        // Make chart responsive
        window.addEventListener('resize', () => {
            chart.resize();
        });
    </script>
</body>
</html>`;

    return html;
  }

  /**
   * Open chart in new window
   */
  async openChart(message) {
    try {
      const chartHTML = this.generateChartHTML(message);
      const chartType = this.determineChartType(message);
      
      // Create blob URL for the HTML content
      const blob = new Blob([chartHTML], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      
      // Open in new window
      const windowFeatures = [
        'width=1200',
        'height=800',
        'scrollbars=yes',
        'resizable=yes',
        'toolbar=no',
        'menubar=no',
        'location=no',
        'status=no'
      ].join(',');
      
      const chartWindow = window.open(url, '_blank', windowFeatures);
      
      if (chartWindow) {
        chartWindow.focus();
        
        // Clean up blob URL after window loads
        chartWindow.addEventListener('load', () => {
          setTimeout(() => URL.revokeObjectURL(url), 1000);
        });
        
        return {
          success: true,
          chartType: chartType,
          window: chartWindow
        };
      } else {
        throw new Error('Failed to open chart window. Please check your popup blocker settings.');
      }
      
    } catch (error) {
      console.error('Error opening chart:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get available chart types
   */
  getAvailableChartTypes() {
    return Object.entries(this.chartTypes).map(([key, value]) => ({
      type: key,
      name: value
    }));
  }
}

// Export singleton instance
const chartService = new ChartService();
export default chartService;
