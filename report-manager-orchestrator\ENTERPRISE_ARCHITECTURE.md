# 🏗️ Enterprise Multi-Database Query System - Architecture Documentation

## 📋 Table of Contents
- [System Overview](#-system-overview)
- [Architecture Components](#️-architecture-components)
- [Query Flow](#-query-flow)
- [Configuration Guide](#️-configuration-guide)
- [Database Support](#️-database-support)
- [API Reference](#-api-reference)
- [Adding New Databases](#-adding-new-databases)
- [Troubleshooting](#-troubleshooting)

---

## 🎯 System Overview

This is a **truly dynamic, AI-powered, multi-database query orchestrator** that converts natural language into SQL queries and routes them to appropriate databases automatically.

### Key Features
- ✅ **Zero Hardcoding**: All behavior is configuration-driven
- ✅ **Natural Language Processing**: Converts human text to SQL
- ✅ **Multi-Database Support**: SQLite, Excel, PostgreSQL, MySQL, etc.
- ✅ **Intelligent Routing**: AI determines which database to query
- ✅ **Dynamic Schema Discovery**: Automatically learns database structures
- ✅ **Plug-and-Play**: Add new databases without code changes

### Current Database Support
| Database | Status | Example Query |
|----------|--------|---------------|
| **SQLite** (invoicing_db) | ✅ Active | "Get all tax codes from einvoicing" |
| **Excel** (employee_db) | ✅ Active | "Show employees with >3 years service" |
| **Oracle** (sales_db) | ✅ Active | "Show top performing sales reps by revenue" |
| **PostgreSQL** | 🔧 Ready | Configuration-ready |
| **MySQL** | 🔧 Ready | Configuration-ready |

---

## 🏛️ Architecture Components

### 1. Configuration Layer (`config/database_config.yaml`)
The brain of the system - defines all databases, routing rules, and behavior.

### 2. Query Engine (`src/report_manager/database/query_engine.py`)
Main orchestrator that coordinates all components.

### 3. SQL Agent (`src/report_manager/agents/sql_agent.py`)
AI-powered component that generates SQL from natural language.

### 4. Connection Manager (`src/report_manager/database/connection_manager.py`)
Handles connections to multiple database types.

### 5. Schema Discovery (`src/report_manager/database/schema_discovery.py`)
Automatically discovers database structures and metadata.

---

## 🔄 Query Flow

```
1. User Input: "I need employees with >3 years service"
   ↓
2. Intent Analysis: Keywords ["employees", "years", "service"]
   ↓
3. Database Routing: Routes to employee_db (Excel)
   ↓
4. Schema Discovery: Finds "YearsOfService" column
   ↓
5. SQL Generation: "SELECT * FROM Employees WHERE YearsOfService > 3"
   ↓
6. Query Execution: Filters Excel data using pandas
   ↓
7. Response: Returns 37 employees (not all 85)
```

---

## ⚙️ Configuration Guide

### Database Configuration
```yaml
# config/database_config.yaml
data_sources:
  invoicing_db:
    name: "Invoicing Database (SQLite)"
    type: "sqlite"
    connection:
      database: "${INVOICING_DB_PATH:data/invoicing.db}"
    keywords: ["tax", "invoice", "billing", "gst", "vat", "einvoicing"]

  employee_db:
    name: "Employee Database (Excel)"
    type: "excel"
    connection:
      file_path: "${EMPLOYEE_DB_PATH:data/employees.xlsx}"
      sheet_name: "Employees"
    keywords: ["employee", "staff", "hr", "department", "salary"]
```

### Routing Rules
```yaml
routing_rules:
  intents:
    employee_queries:
      keywords: ["employee", "staff", "hr", "department", "salary"]
      primary_datasource: "employee_db"
      priority: 1
    
    tax_queries:
      keywords: ["tax", "codes", "rates", "gst", "vat"]
      primary_datasource: "invoicing_db"
      priority: 2
```

---

## 🗄️ Database Support

### SQLite Database (invoicing_db)
- **File**: `data/invoicing.db`
- **Tables**: `eInvoicing_TaxCodeLookup`
- **Example Queries**:
  - "Get all tax codes from einvoicing"
  - "Show me GST tax rates"
  - "List all VAT codes"

### Excel Database (employee_db)
- **File**: `data/employees.xlsx`
- **Sheet**: `Employees`
- **Columns**: EmployeeID, FullName, Department, YearsOfService, Salary, etc.
- **Example Queries**:
  - "Show employees with more than 3 years service"
  - "List HR department employees"
  - "Find employees with salary over 80000"

---

## 🌐 API Reference

### Primary Endpoint
```http
POST /query/excel-clean
Content-Type: application/json

{
  "text": "I need all employees with more than 3 years of service"
}
```

### Response Format
```json
{
  "success": true,
  "sheets": [{
    "name": "Employees",
    "headers": ["EmployeeID", "FullName", "Department", "YearsOfService", "Salary"],
    "rows": [
      [1001, "John Doe", "Engineering", 4.2, 95000],
      [1002, "Jane Smith", "HR", 3.8, 78000]
    ]
  }],
  "metadata": {
    "title": "Report: I need all employees with more than 3 years of service",
    "sql_query": "SELECT * FROM Employees WHERE YearsOfService > 3",
    "datasource": "employee_db",
    "confidence": 1.0,
    "ai_engine": "database_query_engine"
  }
}
```

### Health Check
```http
GET /health

Response:
{
  "status": "healthy",
  "enterprise_ready": true,
  "ai_engine": "database_query_engine"
}
```

---

## 🔧 Adding New Databases

### Step 1: Update Configuration
Add to `config/database_config.yaml`:

```yaml
data_sources:
  sales_db:
    name: "Sales Database"
    type: "postgresql"  # or mysql, sqlserver, etc.
    connection:
      host: "sales-server.company.com"
      port: 5432
      database: "sales"
      username: "${SALES_DB_USER}"
      password: "${SALES_DB_PASSWORD}"
    keywords: ["sales", "revenue", "orders", "customers", "products"]
```

### Step 2: Add Routing Rules
```yaml
routing_rules:
  intents:
    sales_queries:
      keywords: ["sales", "revenue", "orders", "customers"]
      primary_datasource: "sales_db"
      priority: 1
```

### Step 3: Set Environment Variables
```bash
# .env file
SALES_DB_USER=sales_user
SALES_DB_PASSWORD=sales_password
```

### Step 4: Restart Server
```bash
python enterprise_server.py
```

### Step 5: Test
```bash
curl -X POST http://localhost:8009/query/excel-clean \
  -H "Content-Type: application/json" \
  -d '{"text": "Show me sales from last month"}'
```

---

## 🚀 Example Queries

### Employee Queries (Excel Database)
```
✅ "Show me all employees"
✅ "Find employees with more than 3 years of service"
✅ "List HR department employees"
✅ "Get employees with salary over 80000"
✅ "Show engineering department staff"
✅ "Find employees hired in last 2 years"
```

### Tax Code Queries (SQLite Database)
```
✅ "Get all tax codes from einvoicing"
✅ "Show me GST tax rates"
✅ "List all tax codes"
✅ "Find CGST tax codes"
✅ "Show VAT tax information"
```

---

## 🔍 Troubleshooting

### Common Issues

#### 1. "No connection available for data source"
**Cause**: Database connection failed
**Solution**: 
- Check database file exists
- Verify connection parameters in config
- Check environment variables

#### 2. "No relevant data sources found"
**Cause**: Query keywords don't match any configured database
**Solution**:
- Check routing rules in `config/database_config.yaml`
- Add missing keywords to appropriate database configuration

#### 3. "SQL generation failed"
**Cause**: AI couldn't generate appropriate SQL
**Solution**:
- Use more specific keywords
- Check if table/column names exist in database
- Verify schema discovery is working

### Debug Mode
Enable detailed logging by setting:
```python
# In enterprise_server.py
logger.setLevel("DEBUG")
```

### Health Check
Always verify system status:
```bash
curl http://localhost:8009/health
```

---

## 📁 Project Structure

```
report-manager-orchestrator/
├── config/
│   └── database_config.yaml          # Main configuration
├── data/
│   ├── invoicing.db                  # SQLite database
│   └── employees.xlsx                # Excel database
├── src/report_manager/
│   ├── database/
│   │   ├── query_engine.py           # Main orchestrator
│   │   ├── connection_manager.py     # Database connections
│   │   ├── schema_discovery.py       # Schema detection
│   │   └── config_manager.py         # Configuration loader
│   └── agents/
│       └── sql_agent.py              # AI SQL generation
├── tests/                            # All test files (organized)
│   ├── functional/                   # End-to-end tests
│   ├── integration/                  # Component integration tests
│   ├── unit/                         # Unit tests
│   └── README.md                     # Test documentation
├── enterprise_server.py              # Main server
├── run_tests.py                      # Test runner script
└── ENTERPRISE_ARCHITECTURE.md        # This documentation
```

---

## 🎯 Key Benefits

### For Developers
- **Zero Hardcoding**: All behavior is configuration-driven
- **Easy Extension**: Add databases without code changes
- **AI-Powered**: Natural language understanding built-in
- **Type Safety**: Comprehensive error handling

### For Users
- **Natural Language**: No SQL knowledge required
- **Multi-Database**: Query different systems seamlessly
- **Intelligent Routing**: System finds the right data automatically
- **Rich Responses**: Excel-ready formatted results

### For Enterprise
- **Scalable**: Handles multiple databases and users
- **Maintainable**: Configuration-based management
- **Secure**: Built-in access control and validation
- **Future-Proof**: Easy to extend and modify

---

## 📞 Support

For questions or issues:
1. Check this documentation
2. Review configuration files
3. Check server logs
4. Verify database connections
5. Test with health endpoint

**Remember**: This system is designed to be completely dynamic and configuration-driven. No code changes should be needed for adding new databases or query patterns!
