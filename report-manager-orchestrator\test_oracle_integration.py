#!/usr/bin/env python3
"""
Test Oracle Database Integration

This script tests the Oracle database integration with the Report Manager Orchestrator.
"""

import os
import sys
import asyncio
from pathlib import Path
from loguru import logger

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from report_manager.database.config_manager import DatabaseConfigManager
    from report_manager.database.connection_manager import DatabaseConnectionManager
    from report_manager.database.security import DatabaseSecurity, UserSession
    from report_manager.database.schema_discovery import DynamicSchemaDiscovery
    from report_manager.database.query_engine import DatabaseQueryEngine
except ImportError as e:
    logger.error(f"Failed to import report manager modules: {e}")
    sys.exit(1)

async def test_oracle_connection():
    """Test Oracle database connection"""
    logger.info("🔗 Testing Oracle database connection...")
    
    try:
        # Initialize managers
        config_manager = DatabaseConfigManager("config/database_config.yaml")
        security_manager = DatabaseSecurity(config_manager)
        connection_manager = DatabaseConnectionManager(config_manager, security_manager)
        
        # Initialize connections
        await connection_manager.initialize_connections()
        
        # Test Oracle connection specifically
        if "sales_db" in connection_manager.connections:
            logger.info("✅ Oracle sales database connection found")
            
            # Test connection
            test_result = await connection_manager.test_connection("sales_db")
            if test_result:
                logger.info("✅ Oracle connection test successful")
            else:
                logger.error("❌ Oracle connection test failed")
                return False
        else:
            logger.error("❌ Oracle connection not found in connection manager")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Oracle connection test failed: {e}")
        return False

async def test_oracle_schema_discovery():
    """Test Oracle schema discovery"""
    logger.info("🔍 Testing Oracle schema discovery...")
    
    try:
        # Initialize managers
        config_manager = DatabaseConfigManager("config/database_config.yaml")
        security_manager = DatabaseSecurity(config_manager)
        connection_manager = DatabaseConnectionManager(config_manager, security_manager)
        
        # Initialize connections
        await connection_manager.initialize_connections()
        
        # Initialize schema discovery
        schema_discovery = DynamicSchemaDiscovery(connection_manager, config_manager)
        
        # Discover Oracle schema
        tables = await schema_discovery.discover_schema("sales_db", force_refresh=True)
        
        if tables:
            logger.info(f"✅ Discovered {len(tables)} tables in Oracle sales database")
            
            # Display discovered tables
            for table_name, table_info in tables.items():
                logger.info(f"   📋 Table: {table_name}")
                logger.info(f"      Schema: {table_info.schema}")
                logger.info(f"      Columns: {len(table_info.columns)}")
                logger.info(f"      Type: {table_info.table_type}")
                if table_info.description:
                    logger.info(f"      Description: {table_info.description}")
                
                # Show first few columns
                column_names = list(table_info.columns.keys())[:3]
                logger.info(f"      Sample columns: {', '.join(column_names)}")
                logger.info("")
            
            return True
        else:
            logger.error("❌ No tables discovered in Oracle database")
            return False
        
    except Exception as e:
        logger.error(f"❌ Oracle schema discovery failed: {e}")
        return False

async def test_oracle_queries():
    """Test Oracle query execution"""
    logger.info("🔍 Testing Oracle query execution...")
    
    try:
        # Initialize managers
        config_manager = DatabaseConfigManager("config/database_config.yaml")
        security_manager = DatabaseSecurity(config_manager)
        connection_manager = DatabaseConnectionManager(config_manager, security_manager)
        
        # Initialize connections
        await connection_manager.initialize_connections()
        
        # Create user session
        user_session = UserSession(
            user_id="test_user",
            username="test",
            roles=["admin"],
            permissions=["sales_db.*"]
        )
        
        # Test simple queries
        test_queries = [
            "SELECT COUNT(*) as rep_count FROM sales_reps",
            "SELECT COUNT(*) as customer_count FROM customers",
            "SELECT COUNT(*) as product_count FROM products",
            "SELECT territory, COUNT(*) as count FROM sales_reps GROUP BY territory",
            "SELECT first_name, last_name, territory FROM sales_reps WHERE ROWNUM <= 5"
        ]
        
        for query in test_queries:
            logger.info(f"   Executing: {query}")
            
            result = await connection_manager.execute_query(
                "sales_db", query, {}, user_session
            )
            
            if result.success:
                logger.info(f"   ✅ Query successful - {result.row_count} rows returned")
                if result.data and len(result.data) > 0:
                    logger.info(f"   📊 Sample result: {result.data[0]}")
            else:
                logger.error(f"   ❌ Query failed: {result.error}")
                return False
            
            logger.info("")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Oracle query execution failed: {e}")
        return False

async def test_full_integration():
    """Test full Oracle integration with query engine"""
    logger.info("🚀 Testing full Oracle integration...")
    
    try:
        # This would require an LLM client for SQL generation
        # For now, we'll just test the basic components
        logger.info("✅ Basic Oracle integration components working")
        return True
        
    except Exception as e:
        logger.error(f"❌ Full Oracle integration test failed: {e}")
        return False

async def main():
    """Main test function"""
    logger.add(sys.stdout, format="{time} | {level} | {message}", level="INFO")
    
    print("🧪 Oracle Database Integration Test")
    print("=" * 50)
    print()
    
    # Check environment variables
    required_env_vars = [
        "ORACLE_HOST", "ORACLE_PORT", "ORACLE_SERVICE_NAME", 
        "ORACLE_USERNAME", "ORACLE_PASSWORD"
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.warning(f"⚠️ Missing environment variables: {', '.join(missing_vars)}")
        logger.info("Using default values where possible")
    
    # Run tests
    tests = [
        ("Oracle Connection", test_oracle_connection),
        ("Oracle Schema Discovery", test_oracle_schema_discovery),
        ("Oracle Query Execution", test_oracle_queries),
        ("Full Integration", test_full_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"🧪 Running test: {test_name}")
        try:
            result = await test_func()
            results[test_name] = result
            if result:
                logger.info(f"✅ {test_name} - PASSED")
            else:
                logger.error(f"❌ {test_name} - FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} - ERROR: {e}")
            results[test_name] = False
        
        print()
    
    # Summary
    print("📊 Test Results Summary")
    print("-" * 30)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All Oracle integration tests passed!")
        return True
    else:
        print(f"\n⚠️ {total - passed} test(s) failed")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
