.waiting-message {
  display: flex;
  justify-content: flex-start;
  padding: 16px 0;
  animation: fadeIn 0.3s ease-in-out;
}

.waiting-content {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px 24px;
  max-width: 400px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.waiting-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 2s infinite;
}

.waiting-step {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  transition: all 0.3s ease-in-out;
}

.waiting-step.animating {
  transform: scale(1.05);
}

.step-icon {
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.step-text {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
  flex: 1;
}

.waiting-dots {
  display: flex;
  justify-content: center;
  gap: 6px;
  margin-bottom: 16px;
}

.waiting-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #3b82f6;
  animation: bounce 1.4s infinite ease-in-out;
}

.waiting-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.waiting-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.waiting-dots span:nth-child(3) {
  animation-delay: 0s;
}

.progress-indicator {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
  transition: width 0.5s ease-in-out;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: progressShimmer 1.5s infinite;
}

.step-counter {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  font-weight: 500;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes progressShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Blur effect for background */
.chat-container.blurred .message:not(.waiting-message) {
  filter: blur(2px);
  opacity: 0.6;
  transition: all 0.3s ease-in-out;
}

.chat-container.blurred .input-area {
  filter: blur(1px);
  opacity: 0.7;
  transition: all 0.3s ease-in-out;
}

/* Post-response waiting state */
.waiting-message.post-response {
  justify-content: center;
}

.waiting-message.post-response .waiting-content {
  background: #fef3c7;
  border-color: #f59e0b;
}

.waiting-message.post-response .step-icon {
  background: #f59e0b;
}

.waiting-message.post-response .waiting-dots span {
  background-color: #f59e0b;
}

.waiting-message.post-response .progress-fill {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}
