.waiting-message {
  display: flex;
  justify-content: flex-start;
  padding: 12px 0;
  animation: fadeIn 0.3s ease-in-out;
}

.waiting-content {
  background: #f9fafb;
  border-radius: 18px;
  padding: 16px 20px;
  max-width: 350px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}

/* ChatGPT-style blur effect when processing */
.waiting-content.processing {
  filter: blur(0.5px);
  opacity: 0.9;
  animation: subtlePulse 2s infinite ease-in-out;
}

.waiting-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 2s infinite;
}

.waiting-step {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  transition: all 0.3s ease-in-out;
}

.waiting-step.animating {
  transform: scale(1.02);
}

.step-icon {
  font-size: 16px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.step-text {
  font-size: 14px;
  font-weight: 400;
  color: #374151;
  flex: 1;
  line-height: 1.4;
}

.waiting-dots {
  display: flex;
  justify-content: flex-start;
  gap: 3px;
  margin-left: 30px;
  margin-bottom: 8px;
}

.waiting-dots span {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #9ca3af;
  animation: chatGptBounce 1.4s infinite ease-in-out;
}

.waiting-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.waiting-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.waiting-dots span:nth-child(3) {
  animation-delay: 0s;
}

/* Remove progress indicators - ChatGPT style doesn't use them */

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
}

@keyframes chatGptBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes subtlePulse {
  0%, 100% {
    opacity: 0.9;
  }
  50% {
    opacity: 0.7;
  }
}

/* Post-response waiting state - more subtle styling */
.waiting-message.post-response .waiting-content {
  background: #fefbf3;
  border: 1px solid #fbbf24;
}

.waiting-message.post-response .waiting-dots span {
  background-color: #f59e0b;
}
