import apiConfig from '../config/apiConfig.js';

// Streaming API Service for downloadable reports
class StreamingApiService {
  constructor() {
    this.baseUrl = apiConfig.orchestrator.baseUrl;
    this.defaultHeaders = apiConfig.general.headers;
  }

  /**
   * Get headers with authentication token
   * @returns {Object} Headers with auth token if available
   */
  getAuthHeaders() {
    const headers = { ...this.defaultHeaders };

    // Add auth token if available
    const token = localStorage.getItem('auth_access_token');
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }

  /**
   * Get current user ID from stored user info
   * @returns {string|null} User ID if available
   */
  getCurrentUserId() {
    try {
      const userInfo = localStorage.getItem('auth_user_info');
      if (userInfo) {
        const user = JSON.parse(userInfo);
        return user.sub || user.id || user.email;
      }
    } catch (error) {
      console.warn('Failed to get user ID:', error);
    }
    return null;
  }

  /**
   * Retry logic for failed requests
   * @param {Function} requestFn - Function that makes the request
   * @param {number} maxRetries - Maximum number of retries
   * @param {number} delay - Delay between retries in ms
   * @returns {Promise} Request result
   */
  async retryRequest(requestFn, maxRetries = 3, delay = 1000) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await requestFn();
      } catch (error) {
        lastError = error;

        // Don't retry on auth errors or client errors
        if (error.message.includes('Authentication') ||
            error.message.includes('Access denied') ||
            error.response?.status < 500) {
          throw error;
        }

        if (attempt < maxRetries) {
          console.log(`Request failed, retrying in ${delay}ms... (attempt ${attempt}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, delay));
          delay *= 2; // Exponential backoff
        }
      }
    }

    throw lastError;
  }

  /**
   * Stream a query and generate downloadable report
   * @param {string} text - The user's message/query
   * @param {string} outputFormat - Output format (excel, pdf, csv, etc.)
   * @param {Function} onProgress - Progress callback function
   * @returns {Promise<Object>} Download information
   */
async streamQuery(text, outputFormat = 'excel', onProgress = null) {
  try {
    console.log('StreamingAPI: Starting streamQuery with:', { text, outputFormat });

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 seconds for reports

    console.log('StreamingAPI: Making fetch request to:', `${this.baseUrl}/query/stream`);

    const response = await fetch(`${this.baseUrl}/query/stream`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      signal: controller.signal, // Re-enable this for proper cancellation
      body: JSON.stringify({
        text: text,
        output_format: outputFormat,
        chunk_size: 50, // Optional: control chunk size
        user_id: this.getCurrentUserId(), // Add user context
        timestamp: new Date().toISOString()
      })
    });

    clearTimeout(timeoutId);
    console.log('StreamingAPI: Fetch response received:', response.status, response.statusText);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // Backend always sends streaming data, not files
    console.log('StreamingAPI: Processing streaming response...');
    const result = await this.handleStreamingResponse(response, onProgress);
    console.log('StreamingAPI: handleStreamingResponse completed with result:', result);
    return result;

  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('Request timeout - report generation took too long');
    }

    // Enhanced error handling
    if (error.response) {
      const status = error.response.status;
      switch (status) {
        case 401:
          throw new Error('Authentication required. Please sign in again.');
        case 403:
          throw new Error('Access denied. You do not have permission to generate reports.');
        case 429:
          throw new Error('Too many requests. Please wait a moment and try again.');
        case 500:
          throw new Error('Server error. Please try again later.');
        default:
          throw new Error(`Server error (${status}). Please try again.`);
      }
    }

    console.error('Streaming API Error:', error);
    throw error;
  }
}

async handleStreamingResponse(response, onProgress = null) {
  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  const chunks = [];
  let excelData = null;

  try {
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;

      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            chunks.push(data);

            // Call progress callback
            if (onProgress) {
              onProgress({
                type: data.type,
                data: data,
                totalChunks: chunks.length,
                message: this.getProgressMessage(data)
              });
            }

            // Handle different chunk types
            if (data.type === 'metadata') {
              console.log('📊 Processing:', data.query);
            } else if (data.type === 'data') {
              console.log('📈 Received data chunk');
            } else if (data.type === 'complete') {
              console.log('✅ Streaming complete');
              
              // Reconstruct Excel data from chunks
              excelData = this.reconstructExcelData(chunks);
              
              return {
                success: true,
                excelData: excelData,
                chunks: chunks,
                agentsUsed: data.agents_used,
                executionTime: data.execution_time,
                message: 'Report generated successfully!'
              };
            } else if (data.type === 'error') {
              throw new Error(data.error);
            }

          } catch (parseError) {
            console.warn('Failed to parse chunk:', line, parseError);
          }
        }
      }
    }
  } finally {
    reader.releaseLock();
  }

  // If we get here without a complete signal, something went wrong
  throw new Error('Stream ended unexpectedly');
}

getProgressMessage(data) {
  switch (data.type) {
    case 'metadata':
      return `📊 Processing: ${data.query}`;
    case 'data':
      if (data.chunk && data.chunk.type === 'headers') {
        return `📋 Setting up ${data.chunk.sheet_name} sheet...`;
      } else if (data.chunk && data.chunk.type === 'rows') {
        return `📈 Loading data for ${data.chunk.sheet_name}...`;
      }
      return '📈 Receiving data...';
    case 'complete':
      return '✅ Report completed!';
    case 'error':
      return `❌ Error: ${data.error}`;
    default:
      return 'Processing...';
  }
}

// Helper method to reconstruct Excel data from stream chunks
reconstructExcelData(chunks) {
  const sheets = {};
  let metadata = {};

  // Process data chunks to rebuild Excel structure
  chunks.forEach(chunk => {
    if (chunk.type === 'metadata') {
      metadata = {
        title: `Report: ${chunk.query}`,
        generated_at: chunk.timestamp,
        query: chunk.query
      };
    } else if (chunk.type === 'data' && chunk.chunk) {
      const chunkData = chunk.chunk;
      
      if (chunkData.type === 'headers') {
        const sheetName = chunkData.sheet_name || 'Sheet1';
        if (!sheets[sheetName]) {
          sheets[sheetName] = { headers: [], rows: [] };
        }
        sheets[sheetName].headers = chunkData.headers || [];
        
        // Merge any additional metadata
        if (chunkData.metadata) {
          metadata = { ...metadata, ...chunkData.metadata };
        }
      } else if (chunkData.type === 'rows') {
        const sheetName = chunkData.sheet_name || 'Sheet1';
        if (sheets[sheetName]) {
          sheets[sheetName].rows.push(...(chunkData.rows || []));
        }
      }
    }
  });

  return {
    metadata,
    sheets: Object.entries(sheets).map(([name, data]) => ({
      name,
      headers: data.headers,
      rows: data.rows
    }))
  };
}

// Optional: Add a non-streaming method for simple Excel requests
async queryWithExcel(text, outputFormat = 'excel') {
  try {
    const response = await fetch(`${this.baseUrl}/query/excel`, {
      method: 'POST',
      headers: this.defaultHeaders,
      body: JSON.stringify({
        text: text,
        output_format: outputFormat
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.success) {
      return {
        success: true,
        excelData: result.excel_data,
        agentsUsed: result.agents_used,
        executionTime: result.execution_time,
        message: result.message
      };
    } else {
      throw new Error(result.error || 'Query failed');
    }
  } catch (error) {
    console.error('Excel API Error:', error);
    throw error;
  }
}

  /**
   * Handle file download response
   * @param {Response} response - Fetch response
   * @param {string} contentDisposition - Content disposition header
   * @param {string} outputFormat - Expected output format
   * @returns {Promise<Object>} Download information
   */
  async handleFileDownload(response, contentDisposition, outputFormat) {
    try {
      const blob = await response.blob();

      console.log('File download blob:', {
        size: blob.size,
        type: blob.type,
        contentDisposition
      });

      // Extract filename from content-disposition header
      let filename = `report_${Date.now()}.${outputFormat}`;
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '');
        }
      }

      // Ensure proper file extension
      if (!filename.includes('.')) {
        const extensions = {
          'excel': 'xlsx',
          'pdf': 'pdf',
          'csv': 'csv',
          'word': 'docx'
        };
        filename += `.${extensions[outputFormat] || outputFormat}`;
      }

      // Create download URL
      const downloadUrl = URL.createObjectURL(blob);

      console.log('Download prepared:', {
        filename,
        size: blob.size,
        downloadUrl: downloadUrl.substring(0, 50) + '...'
      });

      return {
        success: true,
        type: 'download',
        downloadUrl: downloadUrl,
        filename: filename,
        size: blob.size,
        format: outputFormat,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('File download error:', error);
      throw new Error(`Failed to process download: ${error.message}`);
    }
  }

  /**
   * Handle streaming JSON response
   * @param {Response} response - Fetch response
   * @param {Function} onProgress - Progress callback
   * @returns {Promise<Object>} Streaming result
   */
  async handleStreamingResponse(response, onProgress) {
    console.log('🔄 handleStreamingResponse: CALLED!');
    try {
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let result = null;
      let streamingData = {
        sheets: {},
        metadata: null,
        completed: false
      };

      console.log('🔄 handleStreamingResponse: Starting streaming response processing...');

      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          console.log('Streaming completed');
          break;
        }

        // Decode the chunk and add to buffer
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;
        console.log('Received chunk:', chunk);

        // Process complete lines
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep incomplete line in buffer

        for (const line of lines) {
          const trimmedLine = line.trim();
          if (!trimmedLine) continue;

          try {
            // Handle Server-Sent Events format
            if (trimmedLine.startsWith('data: ')) {
              const jsonData = trimmedLine.slice(6); // Remove 'data: ' prefix
              if (jsonData === '[DONE]') {
                console.log('Streaming marked as done');
                streamingData.completed = true;
                break;
              }

              const data = JSON.parse(jsonData);
              console.log('Parsed streaming data:', data);

              // Handle different data types
              if (data.type === 'metadata') {
                streamingData.metadata = data;
                console.log('📊 Metadata received:', data);
              } else if (data.type === 'data' && data.chunk) {
                this.processDataChunk(streamingData, data.chunk);
                console.log('📈 Data chunk processed:', data.chunk.type);
              } else if (data.type === 'complete') {
                streamingData.completed = true;
                console.log('✅ Stream marked as complete');

                // Create final result
                result = {
                  success: true,
                  type: 'complete',
                  excelData: this.createExcelDataFromStreaming(streamingData),
                  agentsUsed: data.agents_used,
                  executionTime: data.execution_time,
                  message: 'Report generated successfully!'
                };
              } else if (data.type === 'error') {
                throw new Error(data.error || 'Stream processing error');
              }

              // Handle progress updates
              if (onProgress && (data.progress || data.status || data.type)) {
                onProgress({
                  message: data.message || data.status || `Processing ${data.type}...`,
                  percentage: data.progress || data.percentage || 0,
                  ...data
                });
              }
            }
            // Handle plain JSON lines
            else if (trimmedLine.startsWith('{')) {
              const data = JSON.parse(trimmedLine);
              console.log('Parsed JSON line:', data);

              // Handle data chunks for Excel generation
              if (data.type === 'data' && data.chunk) {
                this.processDataChunk(streamingData, data.chunk);
              }

              // Handle progress updates
              if (onProgress && (data.progress || data.status || data.type)) {
                onProgress({
                  message: data.message || data.status || `Processing ${data.type}...`,
                  percentage: data.progress || data.percentage || 0,
                  ...data
                });
              }

              // Store the result
              if (data.result || data.success !== undefined) {
                result = data;
              }
            }
          } catch (parseError) {
            console.warn('Failed to parse streaming line:', trimmedLine, parseError);
          }
        }
      }

      // Process any remaining data in buffer
      if (buffer.trim()) {
        try {
          const data = JSON.parse(buffer.trim());
          console.log('Parsed final buffer data:', data);
          if (data.result || data.success !== undefined) {
            result = data;
          }
        } catch (parseError) {
          console.warn('Failed to parse final buffer:', buffer, parseError);
        }
      }

      // If we have streaming data, create a proper result
      if (Object.keys(streamingData.sheets).length > 0) {
        const excelData = {
          metadata: streamingData.metadata || { title: 'Generated Report' },
          sheets: Object.entries(streamingData.sheets).map(([name, data]) => ({
            name,
            headers: data.headers,
            rows: data.rows
          }))
        };

        return {
          success: true,
          type: 'complete',
          excelData: excelData,
          sheets: Object.values(streamingData.sheets),
          metadata: streamingData.metadata,
          message: 'Report generated successfully!',
          timestamp: new Date()
        };
      }

      // If we have a result from the stream, return it
      if (result) {
        return {
          success: true,
          type: 'complete',
          ...result,
          timestamp: new Date()
        };
      }

      // If no data was received, return an error with debug info
      console.error('No data received from stream. Debug info:', {
        streamingDataSheets: Object.keys(streamingData.sheets),
        streamingDataCompleted: streamingData.completed,
        result: result
      });

      return {
        success: false,
        type: 'error',
        message: 'No data received from stream',
        debug: {
          sheetsReceived: Object.keys(streamingData.sheets).length,
          completed: streamingData.completed,
          hasResult: !!result
        },
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Streaming response error:', error);
      throw new Error(`Streaming failed: ${error.message}`);
    }
  }

  /**
   * Process data chunks from streaming response
   * @param {Object} streamingData - Accumulator for streaming data
   * @param {Object} chunk - Data chunk from stream
   */
  processDataChunk(streamingData, chunk) {
    try {
      const sheetName = chunk.sheet_name || 'Data';

      // Initialize sheet if not exists
      if (!streamingData.sheets[sheetName]) {
        streamingData.sheets[sheetName] = {
          headers: [],
          rows: [],
          metadata: null
        };
      }

      const sheet = streamingData.sheets[sheetName];

      // Handle headers
      if (chunk.type === 'headers' && chunk.headers) {
        sheet.headers = chunk.headers;
        sheet.metadata = chunk.metadata;
        streamingData.metadata = chunk.metadata;
        console.log(`Headers set for sheet "${sheetName}":`, chunk.headers);
      }

      // Handle rows
      if (chunk.type === 'rows' && chunk.rows) {
        sheet.rows.push(...chunk.rows);
        console.log(`Added ${chunk.rows.length} rows to sheet "${sheetName}"`);
      }

    } catch (error) {
      console.error('Error processing data chunk:', error);
    }
  }

  /**
   * Create Excel data structure from streaming data
   * @param {Object} streamingData - Accumulated streaming data
   * @returns {Object} Excel data structure
   */
  createExcelDataFromStreaming(streamingData) {
    return {
      metadata: streamingData.metadata || {
        title: 'Generated Report',
        generated_at: new Date().toISOString()
      },
      sheets: Object.entries(streamingData.sheets).map(([name, data]) => ({
        name,
        headers: data.headers || [],
        rows: data.rows || []
      }))
    };
  }

  /**
   * Convert Excel data to downloadable blob
   * @param {Object} excelData - Excel data structure
   * @param {string} filename - Desired filename
   * @returns {Object} Download information
   */
  createDownloadFromExcelData(excelData, filename = null) {
    try {
      // Generate filename if not provided
      if (!filename) {
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        const title = excelData.metadata?.title || 'Report';
        const cleanTitle = title.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
        filename = `${cleanTitle}_${timestamp}.xlsx`;
      }

      // Create CSV content as fallback (since we don't have xlsx library)
      let csvContent = '';

      if (excelData.sheets && excelData.sheets.length > 0) {
        const sheet = excelData.sheets[0]; // Use first sheet

        // Add headers
        if (sheet.headers && sheet.headers.length > 0) {
          csvContent += sheet.headers.join(',') + '\n';
        }

        // Add rows
        if (sheet.rows && sheet.rows.length > 0) {
          sheet.rows.forEach(row => {
            // Escape commas and quotes in CSV
            const escapedRow = row.map(cell => {
              const cellStr = String(cell || '');
              if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
                return `"${cellStr.replace(/"/g, '""')}"`;
              }
              return cellStr;
            });
            csvContent += escapedRow.join(',') + '\n';
          });
        }
      }

      // Create blob
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const downloadUrl = URL.createObjectURL(blob);

      // For CSV, change extension
      const csvFilename = filename.replace('.xlsx', '.csv');

      return {
        success: true,
        type: 'download',
        downloadUrl: downloadUrl,
        filename: csvFilename,
        size: blob.size,
        format: 'csv',
        timestamp: new Date(),
        metadata: excelData.metadata
      };
    } catch (error) {
      console.error('Error creating download:', error);
      throw new Error(`Failed to create download: ${error.message}`);
    }
  }

  /**
   * Generate and download report
   * @param {string} userMessage - The user's query
   * @param {string} format - Report format (excel, pdf, csv)
   * @param {Function} onProgress - Progress callback
   * @returns {Promise<Object>} Download result
   */
  async generateReport(userMessage, format = 'excel', onProgress = null) {
    try {
      const result = await this.streamQuery(userMessage, format, onProgress);

      if (result.type === 'complete' && result.excelData) {
        // Convert Excel data to downloadable file
        const downloadInfo = this.createDownloadFromExcelData(result.excelData);

        return {
          success: true,
          message: `${format.toUpperCase()} report generated successfully`,
          downloadInfo: downloadInfo,
          excelData: result.excelData,
          metadata: result.metadata,
          timestamp: result.timestamp
        };
      } else if (result.type === 'download') {
        // Direct download response
        return {
          success: true,
          message: `${format.toUpperCase()} report generated successfully`,
          downloadInfo: result,
          timestamp: result.timestamp
        };
      } else {
        return {
          success: false,
          message: 'Report generation did not return usable data',
          result: result,
          timestamp: new Date()
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `Failed to generate report: ${error.message}`,
        error: error.message,
        timestamp: new Date()
      };
    }
  }
}

// Create and export singleton instance
const streamingApi = new StreamingApiService();

export default streamingApi;
export { StreamingApiService };
