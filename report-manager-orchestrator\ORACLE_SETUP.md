# Oracle Database Integration Setup Guide

This guide explains how to set up and use Oracle database integration with the Report Manager Orchestrator.

## 🎯 Overview

The Oracle integration adds support for:
- **Sales Management System**: Complete sales tracking with customers, products, transactions, and targets
- **Oracle Database Connectivity**: Native Oracle database support via Docker
- **Schema Discovery**: Automatic table and column discovery
- **Natural Language Queries**: AI-powered SQL generation for Oracle

## 📋 Prerequisites

### 1. Docker Desktop
Install Docker Desktop for running Oracle Database Free container:
- Download from: https://www.docker.com/products/docker-desktop/

### 2. Python Dependencies
Oracle database drivers are already included in requirements.txt:
```bash
pip install -r requirements.txt
```

### 3. Environment Variables
Set the following environment variables in your `.env` file:

```bash
# Oracle Database Connection (Docker)
ORACLE_HOST=localhost
ORACLE_PORT=1521
ORACLE_SERVICE_NAME=FREE
ORACLE_USERNAME=system
ORACLE_PASSWORD=oracle123
```

## 🚀 Quick Setup

### Step 1: Start Oracle Docker Container
```bash
# Start Oracle Database Free container
docker run -d --name oracle-free -p 1521:1521 -e ORACLE_PWD=oracle123 \
  container-registry.oracle.com/database/free:latest

# Wait 2-3 minutes for container to fully start
docker logs oracle-free
```

### Step 2: Set Environment Variables
Ensure your `.env` file has:
```env
ORACLE_HOST=localhost
ORACLE_PORT=1521
ORACLE_SERVICE_NAME=FREE
ORACLE_USERNAME=system
ORACLE_PASSWORD=oracle123
```

### Step 3: Create Sales Database Schema
```bash
# Create the sales database with sample data
python create_oracle_db.py
```

### Step 4: Test Integration
```bash
# Test Oracle connectivity and features
python test_oracle_integration.py
```

### Step 5: Start the Server
```bash
# Start the orchestrator server
python enterprise_server.py
```

## 📊 Database Schema

The Oracle integration creates the following sales tables:

### `sales_reps`
- Sales representatives with territories and commission rates
- Contact information and hire dates
- Manager relationships and status tracking

### `customers`
- Customer companies with contact information
- Industry classification and customer types
- Address and communication details

### `products`
- Product catalog with categories and pricing
- Unit prices, cost prices, and descriptions
- Product status and creation tracking

### `sales_transactions`
- Sales transactions linking reps, customers, and products
- Transaction amounts, quantities, and discounts
- Transaction dates and status tracking

### `sales_targets`
- Quarterly and annual sales targets
- Target vs actual performance tracking
- Territory and rep-specific goals

### `sales_metrics`
- Sales KPIs and performance measurements
- Conversion rates, deal sizes, win rates
- Time-series sales performance data

## 🔍 Sample Queries

Once set up, you can ask natural language questions like:

### Sales Performance Queries
```
✅ "Show all sales reps in North territory"
✅ "Find top performing sales reps by revenue"
✅ "List customers by industry"
✅ "Show sales targets vs actual by quarter"
```

### Sales Analytics
```
✅ "What's the average deal size by territory?"
✅ "Show sales trends for the last quarter"
✅ "Find best selling products by category"
✅ "Get conversion rates by sales rep"
```

### Sales Reports
```
✅ "Generate sales summary by territory"
✅ "Show quota attainment by rep"
✅ "List customers with highest transaction volume"
✅ "Find products with highest profit margins"
```

## ⚙️ Configuration

The Oracle database is configured in `config/database_config.yaml`:

```yaml
data_sources:
  sales_db:
    name: "Sales Database (Oracle)"
    description: "Oracle database with sales data including customers, products, transactions, and targets"
    type: "oracle"
    connection:
      host: "${ORACLE_HOST:localhost}"
      port: "${ORACLE_PORT:1521}"
      service_name: "${ORACLE_SERVICE_NAME:FREE}"
      username: "${ORACLE_USERNAME:hr}"
      password: "${ORACLE_PASSWORD:hr}"

    auto_discovery:
      enabled: true
      include_schemas: ["HR"]
      exclude_tables: ["DUAL", "USER_TABLES", "ALL_TABLES"]
      include_views: false
      cache_schema: true

    business_context:
      domain: "sales"
      keywords: ["sales", "revenue", "customer", "product", "transaction", "target", "quota", "territory"]
```

## 🔧 Troubleshooting

### Connection Issues

**Problem**: Cannot connect to Oracle database
```
❌ Failed to connect to Oracle: ORA-12541: TNS:no listener
```

**Solutions**:
1. Check Oracle service is running
2. Verify host and port settings
3. Ensure firewall allows connections
4. Check TNS configuration

### Authentication Issues

**Problem**: Invalid username/password
```
❌ Failed to connect to Oracle: ORA-01017: invalid username/password
```

**Solutions**:
1. Verify credentials in environment variables
2. Check user exists and has proper permissions
3. Ensure account is not locked

### Schema Discovery Issues

**Problem**: No tables discovered
```
❌ No tables discovered in Oracle database
```

**Solutions**:
1. Check schema name in configuration
2. Verify user has SELECT permissions on tables
3. Ensure tables exist in the specified schema

## 🏗️ Oracle Database Setup (Docker)

### Recommended: Oracle Database Free (Docker)
```bash
# Start Oracle Database Free container
docker run -d --name oracle-free -p 1521:1521 -e ORACLE_PWD=oracle123 \
  container-registry.oracle.com/database/free:latest

# Check container status
docker ps

# Wait for database to be ready (2-3 minutes)
docker logs oracle-free

# Verify connection
docker exec -it oracle-free sqlplus system/oracle123@FREE
```

### Container Management
```bash
# Stop container
docker stop oracle-free

# Start existing container
docker start oracle-free

# Remove container (will lose data)
docker rm oracle-free

# View container logs
docker logs oracle-free
```

## 📈 Performance Optimization

### Connection Pooling
The system automatically uses connection pooling for Oracle:
- Pool size: 10 connections (configurable)
- Connection timeout: 30 seconds
- Connection recycling: 1 hour

### Query Optimization
- Automatic query result limiting (10,000 rows max)
- Schema caching (1 hour TTL)
- Prepared statement usage for security

### Monitoring
- Query execution time logging
- Connection pool monitoring
- Error tracking and reporting

## 🔐 Security Features

### SQL Injection Protection
- Parameterized queries only
- Query validation and sanitization
- Restricted SQL operations (SELECT only by default)

### Access Control
- Role-based permissions
- Table-level access control
- User session management

### Audit Trail
- Query logging (optional)
- User activity tracking
- Error logging

## 🚀 Next Steps

1. **Set up Oracle database** using one of the options above
2. **Configure environment variables** for your Oracle instance
3. **Run the setup script** to create schema and sample data
4. **Test the integration** using the test script
5. **Start querying** using natural language through the orchestrator

## 📞 Support

For issues with Oracle integration:
1. Check the troubleshooting section above
2. Review Oracle database logs
3. Verify network connectivity and permissions
4. Test with simple SQL queries first

## 🔗 Related Documentation

- [Enterprise Architecture](ENTERPRISE_ARCHITECTURE.md)
- [Quick Start Guide](QUICK_START_GUIDE.md)
- [Database Configuration](config/database_config.yaml)
- [API Documentation](docs/API_GUIDE.md)
