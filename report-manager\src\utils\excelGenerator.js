import * as XLSX from 'xlsx';

/**
 * Excel Generator Utility
 * Converts streaming JSON data to Excel files
 */
class ExcelGenerator {
  
  /**
   * Generate Excel file from streaming API response
   * @param {Object} apiResponse - The API response data
   * @param {string} filename - Desired filename
   * @returns {Object} Download info with blob URL
   */
  static generateFromApiResponse(apiResponse, filename = 'report.xlsx') {
    try {
      console.log('Generating Excel from API response:', apiResponse);
      
      // Create a new workbook
      const workbook = XLSX.utils.book_new();
      
      // Extract data from different parts of the API response
      const sheets = this.extractSheetsFromResponse(apiResponse);
      
      // Add each sheet to the workbook
      sheets.forEach(sheet => {
        const worksheet = XLSX.utils.json_to_sheet(sheet.data);
        XLSX.utils.book_append_sheet(workbook, worksheet, sheet.name);
      });
      
      // Generate Excel file
      const excelBuffer = XLSX.write(workbook, { 
        bookType: 'xlsx', 
        type: 'array' 
      });
      
      // Create blob and download URL
      const blob = new Blob([excelBuffer], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      const downloadUrl = URL.createObjectURL(blob);
      
      return {
        success: true,
        type: 'download',
        downloadUrl,
        filename,
        size: blob.size,
        format: 'excel',
        timestamp: new Date()
      };
      
    } catch (error) {
      console.error('Excel generation failed:', error);
      throw new Error(`Failed to generate Excel: ${error.message}`);
    }
  }
  
  /**
   * Extract sheets data from API response
   * @param {Object} response - API response
   * @returns {Array} Array of sheet objects
   */
  static extractSheetsFromResponse(response) {
    const sheets = [];

    try {
      // Handle different response formats

      // Format 1: Streaming data with actual data chunks (PRIORITY)
      if (response.streamingData && response.streamingData.sheets) {
        console.log('Processing streaming data sheets:', response.streamingData.sheets);

        Object.keys(response.streamingData.sheets).forEach(sheetName => {
          const sheetData = response.streamingData.sheets[sheetName];
          console.log(`Processing sheet "${sheetName}":`, sheetData);

          if (sheetData.rows && sheetData.rows.length > 0) {
            let data;

            if (sheetData.headers && sheetData.headers.length > 0) {
              // Convert headers and rows to array of objects
              data = sheetData.rows.map(row => {
                const obj = {};
                sheetData.headers.forEach((header, index) => {
                  obj[header] = row[index] || '';
                });
                return obj;
              });
            } else {
              // No headers provided, create generic column names
              const maxColumns = Math.max(...sheetData.rows.map(row => row.length));
              const headers = Array.from({length: maxColumns}, (_, i) => `Column_${i + 1}`);

              data = sheetData.rows.map(row => {
                const obj = {};
                headers.forEach((header, index) => {
                  obj[header] = row[index] || '';
                });
                return obj;
              });
            }

            sheets.push({
              name: sheetName,
              data: data
            });

            console.log(`Added sheet "${sheetName}" with ${data.length} rows`);
          }
        });
      }

      // Format 2: Structured data with result.data
      else if (response.result && response.result.data) {
        const data = response.result.data;

        // Add reports sheet if available
        if (data.reports && Array.isArray(data.reports)) {
          sheets.push({
            name: 'Reports',
            data: data.reports
          });
        }

        // Add metrics sheet if available
        if (data.metrics && Array.isArray(data.metrics)) {
          sheets.push({
            name: 'Metrics',
            data: data.metrics
          });
        }

        // Add any other arrays found in data
        Object.keys(data).forEach(key => {
          if (Array.isArray(data[key]) && key !== 'reports' && key !== 'metrics') {
            sheets.push({
              name: this.capitalizeFirst(key),
              data: data[key]
            });
          }
        });
      }

      // Format 3: Direct arrays in response
      else if (response.reports || response.metrics || response.data) {
        if (response.reports && Array.isArray(response.reports)) {
          sheets.push({
            name: 'Reports',
            data: response.reports
          });
        }

        if (response.metrics && Array.isArray(response.metrics)) {
          sheets.push({
            name: 'Metrics',
            data: response.metrics
          });
        }

        if (response.data && Array.isArray(response.data)) {
          sheets.push({
            name: 'Data',
            data: response.data
          });
        }
      }
      
      // If no structured data found, create a summary sheet
      if (sheets.length === 0) {
        sheets.push({
          name: 'Summary',
          data: this.createSummaryData(response)
        });
      }
      
      // Add metadata sheet
      sheets.push({
        name: 'Metadata',
        data: this.createMetadataSheet(response)
      });
      
    } catch (error) {
      console.warn('Error extracting sheets:', error);
      // Fallback: create basic summary
      sheets.push({
        name: 'Data',
        data: [{ 
          'Response': JSON.stringify(response, null, 2),
          'Generated': new Date().toISOString()
        }]
      });
    }
    
    return sheets;
  }
  
  /**
   * Create summary data from response
   * @param {Object} response - API response
   * @returns {Array} Summary data array
   */
  static createSummaryData(response) {
    const summary = [];
    
    // Basic response info
    summary.push({
      'Property': 'Success',
      'Value': response.success ? 'Yes' : 'No'
    });
    
    if (response.agents_used) {
      summary.push({
        'Property': 'Agents Used',
        'Value': Array.isArray(response.agents_used) ? response.agents_used.join(', ') : response.agents_used
      });
    }
    
    if (response.execution_time) {
      summary.push({
        'Property': 'Execution Time',
        'Value': `${response.execution_time.toFixed(2)} seconds`
      });
    }
    
    if (response.type) {
      summary.push({
        'Property': 'Response Type',
        'Value': response.type
      });
    }
    
    return summary;
  }
  
  /**
   * Create metadata sheet
   * @param {Object} response - API response
   * @returns {Array} Metadata array
   */
  static createMetadataSheet(response) {
    const metadata = [
      {
        'Property': 'Generated At',
        'Value': new Date().toISOString()
      },
      {
        'Property': 'Source',
        'Value': 'Report Manager Streaming API'
      }
    ];
    
    if (response.context) {
      metadata.push({
        'Property': 'Intent',
        'Value': response.context.intent || 'Unknown'
      });
      
      metadata.push({
        'Property': 'Confidence',
        'Value': response.context.confidence || 'Unknown'
      });
    }
    
    return metadata;
  }
  
  /**
   * Capitalize first letter of string
   * @param {string} str - Input string
   * @returns {string} Capitalized string
   */
  static capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
  
  /**
   * Generate Excel from simple data array
   * @param {Array} data - Array of objects
   * @param {string} sheetName - Sheet name
   * @param {string} filename - File name
   * @returns {Object} Download info
   */
  static generateFromArray(data, sheetName = 'Data', filename = 'report.xlsx') {
    try {
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(data);
      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
      
      const excelBuffer = XLSX.write(workbook, { 
        bookType: 'xlsx', 
        type: 'array' 
      });
      
      const blob = new Blob([excelBuffer], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      const downloadUrl = URL.createObjectURL(blob);
      
      return {
        success: true,
        type: 'download',
        downloadUrl,
        filename,
        size: blob.size,
        format: 'excel',
        timestamp: new Date()
      };
      
    } catch (error) {
      console.error('Excel generation from array failed:', error);
      throw new Error(`Failed to generate Excel: ${error.message}`);
    }
  }
}

export default ExcelGenerator;
