#!/usr/bin/env python3
"""
Test Groq Integration

This script tests the Groq LLM integration with the Report Manager Orchestrator.
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from report_manager.core.orchestrator import ReportManagerOrchestrator
    from langchain.schema import HumanMessage
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    print("💡 Make sure you're in the correct directory and dependencies are installed")
    sys.exit(1)

def test_groq_connection():
    """Test basic Groq connection and response"""
    print("🔍 Testing Groq LLM connection...")
    
    try:
        # Initialize orchestrator with Groq (default)
        orchestrator = ReportManagerOrchestrator()
        
        print(f"✅ Orchestrator initialized successfully")
        print(f"📋 Model: {orchestrator.model_name}")
        print(f"🌡️ Temperature: {orchestrator.temperature}")
        
        # Test simple message
        test_message = "Hello! Please respond with '<PERSON>roq is working!' to confirm the connection."
        
        print(f"\n🔍 Testing with message: {test_message}")
        
        # Create message
        messages = [HumanMessage(content=test_message)]
        
        # Get response
        response = orchestrator.llm.invoke(messages)
        
        print(f"✅ Response received: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ Groq connection test failed: {e}")
        
        # Provide specific error guidance
        error_str = str(e).lower()
        
        if "api key" in error_str or "authentication" in error_str:
            print("💡 API Key issue:")
            print("   - Check GROQ_API_KEY in .env file")
            print("   - Verify the API key is valid")
            print("   - Ensure .env file is in the correct location")
            
        elif "model" in error_str:
            print("💡 Model issue:")
            print("   - Check GROQ_MODEL in .env file")
            print("   - Verify the model name is correct")
            print("   - Try: gemma2-9b-it, llama3-8b-8192, mixtral-8x7b-32768")
            
        elif "rate limit" in error_str:
            print("💡 Rate limit issue:")
            print("   - Wait a moment and try again")
            print("   - Check your Groq account usage")
            
        return False

def test_orchestrator_functionality():
    """Test orchestrator functionality with Groq"""
    print("\n🔍 Testing orchestrator functionality...")
    
    try:
        # Initialize orchestrator
        orchestrator = ReportManagerOrchestrator()
        
        # Test context creation
        test_query = "Show me employee data from the database"
        
        print(f"🔍 Testing context creation with: {test_query}")
        
        # This would normally call create_context, but let's test the LLM directly
        context_prompt = f"""
        Analyze this user request and determine the task type and context:
        User Request: {test_query}
        
        Respond with a brief analysis of what the user wants.
        """
        
        messages = [HumanMessage(content=context_prompt)]
        response = orchestrator.llm.invoke(messages)
        
        print(f"✅ Context analysis: {response.content[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Orchestrator functionality test failed: {e}")
        return False

def check_environment_variables():
    """Check if required environment variables are set"""
    print("🌍 Checking environment variables...")
    
    required_vars = ['GROQ_API_KEY', 'GROQ_MODEL']
    optional_vars = ['GROQ_TEMPERATURE']
    
    all_good = True
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            if 'API_KEY' in var:
                print(f"✅ {var}: {'*' * len(value)}")
            else:
                print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: Not set")
            all_good = False
    
    for var in optional_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value}")
        else:
            print(f"⚠️ {var}: Not set (using default)")
    
    return all_good

def show_available_models():
    """Show available Groq models"""
    print("\n📋 Popular Groq models you can use:")
    
    models = [
        ("gemma2-9b-it", "Google Gemma 2 9B - Good balance of speed and quality"),
        ("llama3-8b-8192", "Meta Llama 3 8B - Fast and efficient"),
        ("llama3-70b-8192", "Meta Llama 3 70B - High quality, slower"),
        ("mixtral-8x7b-32768", "Mixtral 8x7B - Good for complex tasks"),
        ("gemma-7b-it", "Google Gemma 7B - Lightweight option")
    ]
    
    for model, description in models:
        print(f"   • {model}: {description}")
    
    print(f"\n💡 Current model: {os.getenv('GROQ_MODEL', 'Not set')}")

def main():
    """Main test function"""
    print("🧪 Groq Integration Test")
    print("=" * 50)
    
    # Check environment variables
    env_ok = check_environment_variables()
    print()
    
    if not env_ok:
        print("❌ Environment variables not properly configured")
        print("💡 Please check your .env file")
        return False
    
    # Show available models
    show_available_models()
    
    # Run tests
    tests = [
        ("Groq Connection", test_groq_connection),
        ("Orchestrator Functionality", test_orchestrator_functionality)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running test: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            if result:
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📊 Test Results Summary")
    print("-" * 30)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All Groq integration tests passed!")
        print("💡 You can now use the orchestrator with Groq")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed")
        print("💡 Check the error messages above for troubleshooting")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
