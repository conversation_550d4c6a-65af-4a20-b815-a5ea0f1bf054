#!/usr/bin/env python3
"""
Create Oracle database with sales data

This script creates Oracle database tables for sales management
and populates them with sample data including sales reps, customers,
products, transactions, targets, and metrics.
"""

import os
import sys
from datetime import datetime, timedelta
import random
from typing import List, Dict, Any
from loguru import logger

try:
    import oracledb
    ORACLE_AVAILABLE = True
except ImportError:
    ORACLE_AVAILABLE = False
    logger.error("Oracle database driver not available. Install oracledb package.")

def test_oracle_connectivity():
    """Test basic Oracle connectivity before attempting connection"""
    import socket

    host = os.getenv('ORACLE_HOST', 'localhost')
    port = int(os.getenv('ORACLE_PORT', '1521'))

    logger.info(f"🔌 Testing network connectivity to {host}:{port}")

    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()

        if result == 0:
            logger.info("✅ Network connection successful")
            return True
        else:
            logger.error(f"❌ Cannot reach {host}:{port} (Error: {result})")
            return False
    except Exception as e:
        logger.error(f"❌ Network test failed: {e}")
        return False

def get_oracle_connection():
    """Get Oracle database connection from environment variables"""
    if not ORACLE_AVAILABLE:
        raise ImportError("Oracle database driver not available")

    # Get connection parameters from environment variables
    # Default values for Oracle Docker container
    host = os.getenv('ORACLE_HOST', 'localhost')
    port = int(os.getenv('ORACLE_PORT', '1521'))
    service_name = os.getenv('ORACLE_SERVICE_NAME', 'FREE')
    username = os.getenv('ORACLE_USERNAME', 'system')  # Use system user for Docker
    password = os.getenv('ORACLE_PASSWORD', 'oracle123')  # Docker container password

    logger.info(f"Connecting to Oracle: {username}@{host}:{port}/{service_name}")

    # Test network connectivity first
    if not test_oracle_connectivity():
        logger.error("❌ Network connectivity test failed")
        logger.info("💡 Possible solutions:")
        logger.info("   1. Check if Oracle Database is installed and running")
        logger.info("   2. Verify Oracle services are started in Windows Services")
        logger.info("   3. Check if port 1521 is open")
        raise ConnectionError("Cannot reach Oracle database server")

    # Try multiple service names for Oracle Docker container
    # For Oracle Database Free Docker container, common service names are:
    service_names_to_try = [
        service_name,  # User specified or default
        'FREE'
    ]

    # Remove duplicates while preserving order
    service_names_to_try = list(dict.fromkeys(service_names_to_try))

    for attempt, svc_name in enumerate(service_names_to_try, 1):
        try:
            logger.info(f"🔄 Attempt {attempt}: Trying service name '{svc_name}'")

            # Create connection
            connection = oracledb.connect(
                user=username,
                password=password,
                host=host,
                port=port,
                service_name=svc_name
            )

            logger.info(f"✅ Oracle connection established with service name: {svc_name}")
            return connection

        except Exception as e:
            logger.warning(f"❌ Failed with service name '{svc_name}': {e}")
            if attempt < len(service_names_to_try):
                logger.info(f"🔄 Trying next service name...")
            continue

    # If all attempts failed, provide detailed guidance
    logger.error("❌ All connection attempts failed")
    logger.info("💡 For Oracle Docker container, try these solutions:")
    logger.info("   1. Ensure Docker container is running: docker ps")
    logger.info("   2. Check container logs: docker logs oracle-free")
    logger.info("   3. Wait for container to fully start (may take 2-3 minutes)")
    logger.info("   4. Try connecting as SYSTEM user with oracle123 password")
    logger.info("   5. Create HR user after connecting as SYSTEM")

    raise ConnectionError("Could not connect to Oracle database with any service name")

def setup_hr_user(connection):
    """Set up HR user in Oracle database (when connected as SYSTEM)"""
    cursor = connection.cursor()

    logger.info("👤 Setting up HR user...")

    try:
        # First, check what database we're connected to
        cursor.execute("SELECT SYS_CONTEXT('USERENV', 'CON_NAME') FROM DUAL")
        container_name = cursor.fetchone()[0]
        logger.info(f"📋 Connected to container: {container_name}")

        # Check if HR user already exists
        cursor.execute("SELECT COUNT(*) FROM ALL_USERS WHERE USERNAME = 'HR'")
        user_exists = cursor.fetchone()[0] > 0

        if user_exists:
            logger.info("✅ HR user already exists")

            # Check if user is unlocked
            cursor.execute("SELECT ACCOUNT_STATUS FROM DBA_USERS WHERE USERNAME = 'HR'")
            status_result = cursor.fetchone()
            if status_result:
                account_status = status_result[0]
                logger.info(f"📋 HR user status: {account_status}")

                if 'LOCKED' in account_status:
                    logger.info("🔓 Unlocking HR user...")
                    cursor.execute("ALTER USER hr ACCOUNT UNLOCK")
                    logger.info("✅ HR user unlocked")
        else:
            # Create HR user with proper syntax for Oracle Database Free
            logger.info("🔧 Creating HR user...")

            # For Oracle Database Free, we might need to specify default tablespace
            try:
                cursor.execute("CREATE USER hr IDENTIFIED BY hr DEFAULT TABLESPACE USERS")
                logger.info("✅ HR user created with USERS tablespace")
            except Exception as e1:
                logger.warning(f"⚠️ Failed with USERS tablespace: {e1}")
                try:
                    # Fallback to basic user creation
                    cursor.execute("CREATE USER hr IDENTIFIED BY hr")
                    logger.info("✅ HR user created (basic)")
                except Exception as e2:
                    logger.error(f"❌ Failed to create HR user: {e2}")
                    raise e2

        # Grant necessary permissions with error handling
        logger.info("🔐 Granting permissions to HR user...")

        # Essential permissions first
        essential_permissions = [
            "GRANT CONNECT TO hr",
            "GRANT RESOURCE TO hr",
            "GRANT CREATE SESSION TO hr"
        ]

        for permission in essential_permissions:
            try:
                cursor.execute(permission)
                logger.info(f"✅ {permission}")
            except Exception as e:
                logger.error(f"❌ CRITICAL: {permission} - {e}")
                raise e

        # Additional permissions (non-critical)
        additional_permissions = [
            "GRANT CREATE TABLE TO hr",
            "GRANT CREATE SEQUENCE TO hr",
            "GRANT CREATE VIEW TO hr",
            "GRANT UNLIMITED TABLESPACE TO hr"
        ]

        for permission in additional_permissions:
            try:
                cursor.execute(permission)
                logger.info(f"✅ {permission}")
            except Exception as e:
                logger.warning(f"⚠️ {permission} - {e}")
                # Continue with other permissions

        # Try to grant quota on USERS tablespace if UNLIMITED TABLESPACE failed
        try:
            cursor.execute("ALTER USER hr QUOTA UNLIMITED ON USERS")
            logger.info("✅ Granted quota on USERS tablespace")
        except Exception as e:
            logger.warning(f"⚠️ Could not grant quota on USERS: {e}")

        connection.commit()
        logger.info("🎉 HR user setup completed!")

        # Verify the user can be used
        cursor.execute("SELECT USERNAME, ACCOUNT_STATUS FROM DBA_USERS WHERE USERNAME = 'HR'")
        user_info = cursor.fetchone()
        if user_info:
            logger.info(f"✅ HR user verification: {user_info[0]} - {user_info[1]}")

    except Exception as e:
        logger.error(f"❌ Failed to setup HR user: {e}")
        logger.info("💡 Possible solutions:")
        logger.info("   1. Ensure you're connected as SYSTEM user")
        logger.info("   2. Check if container is fully initialized")
        logger.info("   3. Try connecting to CDB instead of PDB")
        raise
    finally:
        cursor.close()

def create_sales_database_schema(connection):
    """Create sales database schema"""
    cursor = connection.cursor()

    logger.info("🏗️ Creating sales database schema...")

    # Drop tables if they exist (in reverse order due to foreign keys)
    drop_tables = [
        "DROP TABLE sales_metrics CASCADE CONSTRAINTS",
        "DROP TABLE sales_targets CASCADE CONSTRAINTS",
        "DROP TABLE sales_transactions CASCADE CONSTRAINTS",
        "DROP TABLE customers CASCADE CONSTRAINTS",
        "DROP TABLE products CASCADE CONSTRAINTS",
        "DROP TABLE sales_reps CASCADE CONSTRAINTS"
    ]
    
    for drop_sql in drop_tables:
        try:
            cursor.execute(drop_sql)
            logger.info(f"Dropped existing table")
        except Exception:
            pass  # Table might not exist
    
    # Create sales_reps table
    sales_reps_sql = """
    CREATE TABLE sales_reps (
        rep_id NUMBER(10) PRIMARY KEY,
        first_name VARCHAR2(50) NOT NULL,
        last_name VARCHAR2(50) NOT NULL,
        email VARCHAR2(100) UNIQUE NOT NULL,
        territory VARCHAR2(50) NOT NULL,
        hire_date DATE NOT NULL,
        commission_rate NUMBER(5,4) DEFAULT 0.05,
        manager_id NUMBER(10),
        phone VARCHAR2(20),
        status VARCHAR2(20) DEFAULT 'Active',
        created_date DATE DEFAULT SYSDATE,
        updated_date DATE DEFAULT SYSDATE,
        CONSTRAINT fk_sales_manager FOREIGN KEY (manager_id) REFERENCES sales_reps(rep_id)
    )
    """

    # Create customers table
    customers_sql = """
    CREATE TABLE customers (
        customer_id NUMBER(10) PRIMARY KEY,
        company_name VARCHAR2(100) NOT NULL,
        contact_name VARCHAR2(100),
        email VARCHAR2(100),
        phone VARCHAR2(20),
        address VARCHAR2(200),
        city VARCHAR2(50),
        state VARCHAR2(50),
        country VARCHAR2(50),
        industry VARCHAR2(50),
        customer_type VARCHAR2(20) DEFAULT 'Prospect',
        created_date DATE DEFAULT SYSDATE,
        updated_date DATE DEFAULT SYSDATE
    )
    """

    # Create products table
    products_sql = """
    CREATE TABLE products (
        product_id NUMBER(10) PRIMARY KEY,
        product_name VARCHAR2(100) NOT NULL,
        product_category VARCHAR2(50),
        unit_price NUMBER(10,2) NOT NULL,
        cost_price NUMBER(10,2),
        description CLOB,
        status VARCHAR2(20) DEFAULT 'Active',
        created_date DATE DEFAULT SYSDATE,
        updated_date DATE DEFAULT SYSDATE
    )
    """
    
    # Create sales_transactions table
    transactions_sql = """
    CREATE TABLE sales_transactions (
        transaction_id NUMBER(10) PRIMARY KEY,
        customer_id NUMBER(10) NOT NULL,
        rep_id NUMBER(10) NOT NULL,
        product_id NUMBER(10) NOT NULL,
        transaction_date DATE NOT NULL,
        quantity NUMBER(10,2) NOT NULL,
        unit_price NUMBER(10,2) NOT NULL,
        total_amount NUMBER(12,2) NOT NULL,
        discount_percent NUMBER(5,2) DEFAULT 0,
        transaction_type VARCHAR2(20) DEFAULT 'Sale',
        status VARCHAR2(20) DEFAULT 'Completed',
        notes CLOB,
        created_date DATE DEFAULT SYSDATE,
        updated_date DATE DEFAULT SYSDATE,
        CONSTRAINT fk_transaction_customer FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
        CONSTRAINT fk_transaction_rep FOREIGN KEY (rep_id) REFERENCES sales_reps(rep_id),
        CONSTRAINT fk_transaction_product FOREIGN KEY (product_id) REFERENCES products(product_id)
    )
    """
    
    # Create sales_targets table
    targets_sql = """
    CREATE TABLE sales_targets (
        target_id NUMBER(10) PRIMARY KEY,
        rep_id NUMBER(10) NOT NULL,
        target_period VARCHAR2(20) NOT NULL,
        target_amount NUMBER(12,2) NOT NULL,
        actual_amount NUMBER(12,2) DEFAULT 0,
        target_year NUMBER(4) NOT NULL,
        target_quarter NUMBER(1),
        target_month NUMBER(2),
        status VARCHAR2(20) DEFAULT 'Active',
        created_date DATE DEFAULT SYSDATE,
        updated_date DATE DEFAULT SYSDATE,
        CONSTRAINT fk_target_rep FOREIGN KEY (rep_id) REFERENCES sales_reps(rep_id)
    )
    """

    # Create sales_metrics table
    metrics_sql = """
    CREATE TABLE sales_metrics (
        metric_id NUMBER(10) PRIMARY KEY,
        rep_id NUMBER(10) NOT NULL,
        metric_name VARCHAR2(100) NOT NULL,
        metric_value NUMBER(15,4),
        metric_unit VARCHAR2(20),
        measurement_date DATE NOT NULL,
        metric_category VARCHAR2(50),
        target_value NUMBER(15,4),
        created_date DATE DEFAULT SYSDATE,
        CONSTRAINT fk_sales_metric_rep FOREIGN KEY (rep_id) REFERENCES sales_reps(rep_id)
    )
    """
    
    # Execute table creation
    tables = [
        ("sales_reps", sales_reps_sql),
        ("customers", customers_sql),
        ("products", products_sql),
        ("sales_transactions", transactions_sql),
        ("sales_targets", targets_sql),
        ("sales_metrics", metrics_sql)
    ]
    
    for table_name, sql in tables:
        try:
            cursor.execute(sql)
            logger.info(f"✅ Created table: {table_name}")
        except Exception as e:
            logger.error(f"❌ Failed to create table {table_name}: {e}")
            raise
    
    # Create sequences for primary keys
    sequences = [
        "CREATE SEQUENCE rep_seq START WITH 1001 INCREMENT BY 1",
        "CREATE SEQUENCE customer_seq START WITH 1 INCREMENT BY 1",
        "CREATE SEQUENCE product_seq START WITH 1 INCREMENT BY 1",
        "CREATE SEQUENCE transaction_seq START WITH 1 INCREMENT BY 1",
        "CREATE SEQUENCE target_seq START WITH 1 INCREMENT BY 1",
        "CREATE SEQUENCE metric_seq START WITH 1 INCREMENT BY 1"
    ]
    
    for seq_sql in sequences:
        try:
            cursor.execute(seq_sql)
        except Exception:
            pass  # Sequence might already exist
    
    connection.commit()
    cursor.close()
    
    logger.info("🎉 Sales database schema created successfully!")

def generate_sample_sales_data():
    """Generate sample sales data"""

    # Sales territories and roles
    territories = {
        "North": ["Sales Rep", "Senior Sales Rep", "Territory Manager", "Regional Manager"],
        "South": ["Sales Rep", "Senior Sales Rep", "Territory Manager", "Regional Manager"],
        "East": ["Sales Rep", "Senior Sales Rep", "Territory Manager", "Regional Manager"],
        "West": ["Sales Rep", "Senior Sales Rep", "Territory Manager", "Regional Manager"],
        "Central": ["Sales Rep", "Senior Sales Rep", "Territory Manager", "Regional Manager"]
    }
    
    # Generate sales reps
    sales_reps = []
    rep_id = 1001
    
    first_names = ["John", "Jane", "Michael", "Sarah", "David", "Emily", "Robert", "Lisa", 
                   "James", "Maria", "William", "Jennifer", "Richard", "Patricia"]
    last_names = ["Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", 
                  "Davis", "Rodriguez", "Martinez", "Anderson", "Thomas", "Taylor"]
    
    for territory, positions in territories.items():
        territory_size = random.randint(6, 10)

        for _ in range(territory_size):
            first_name = random.choice(first_names)
            last_name = random.choice(last_names)
            position = random.choice(positions)

            # Calculate hire date (6 months to 3 years ago)
            days_ago = random.randint(180, 1095)
            hire_date = datetime.now() - timedelta(days=days_ago)

            # Calculate commission rate based on position
            if "Manager" in position:
                commission_rate = random.uniform(0.08, 0.12)
            elif "Senior" in position:
                commission_rate = random.uniform(0.06, 0.09)
            else:
                commission_rate = random.uniform(0.04, 0.07)

            sales_rep = {
                "rep_id": rep_id,
                "first_name": first_name,
                "last_name": last_name,
                "email": f"{first_name.lower()}.{last_name.lower()}@company.com",
                "territory": territory,
                "position": position,
                "hire_date": hire_date,
                "commission_rate": round(commission_rate, 4),
                "phone": f"555-{random.randint(100, 999)}-{random.randint(1000, 9999)}",
                "status": random.choice(["Active"] * 9 + ["On Leave"])  # 90% active
            }

            sales_reps.append(sales_rep)
            rep_id += 1

    # Generate customers
    customers = []
    customer_id = 1

    industries = ["Technology", "Healthcare", "Finance", "Manufacturing", "Retail", "Education"]
    customer_types = ["Prospect", "Lead", "Customer", "VIP Customer"]

    for i in range(50):  # Generate 50 customers
        company_names = [
            "TechCorp", "HealthPlus", "FinanceFirst", "ManufacturingMax", "RetailRush",
            "EduExcellence", "InnovateInc", "GlobalSolutions", "SmartSystems", "FutureTech"
        ]

        customer = {
            "customer_id": customer_id,
            "company_name": f"{random.choice(company_names)} {random.randint(1, 999)}",
            "contact_name": f"{random.choice(first_names)} {random.choice(last_names)}",
            "email": f"contact{customer_id}@company{customer_id}.com",
            "phone": f"555-{random.randint(100, 999)}-{random.randint(1000, 9999)}",
            "industry": random.choice(industries),
            "customer_type": random.choice(customer_types)
        }

        customers.append(customer)
        customer_id += 1

    # Generate products
    products = []
    product_id = 1

    product_categories = ["Software", "Hardware", "Services", "Consulting", "Training"]
    product_names = [
        "Enterprise Suite", "Analytics Platform", "Security Solution", "Cloud Service",
        "Mobile App", "Database System", "Monitoring Tool", "Integration Platform"
    ]

    for category in product_categories:
        for i in range(random.randint(3, 6)):
            product = {
                "product_id": product_id,
                "product_name": f"{random.choice(product_names)} {category} v{random.randint(1, 5)}.{random.randint(0, 9)}",
                "product_category": category,
                "unit_price": round(random.uniform(100, 5000), 2),
                "cost_price": round(random.uniform(50, 2500), 2)
            }

            products.append(product)
            product_id += 1

    return {
        "sales_reps": sales_reps,
        "customers": customers,
        "products": products
    }

def insert_sample_data(connection, data):
    """Insert sample data into Oracle database"""
    cursor = connection.cursor()

    logger.info("📊 Inserting sample sales data...")

    sales_reps = data["sales_reps"]
    customers = data["customers"]
    products = data["products"]

    # Insert sales reps
    rep_sql = """
    INSERT INTO sales_reps (rep_id, first_name, last_name, email, territory,
                           hire_date, commission_rate, phone, status)
    VALUES (:1, :2, :3, :4, :5, :6, :7, :8, :9)
    """

    rep_data = [
        (rep["rep_id"], rep["first_name"], rep["last_name"], rep["email"],
         rep["territory"], rep["hire_date"], rep["commission_rate"],
         rep["phone"], rep["status"])
        for rep in sales_reps
    ]

    cursor.executemany(rep_sql, rep_data)
    logger.info(f"✅ Inserted {len(sales_reps)} sales reps")

    # Insert customers
    customer_sql = """
    INSERT INTO customers (customer_id, company_name, contact_name, email, phone, industry, customer_type)
    VALUES (:1, :2, :3, :4, :5, :6, :7)
    """

    customer_data = [
        (cust["customer_id"], cust["company_name"], cust["contact_name"],
         cust["email"], cust["phone"], cust["industry"], cust["customer_type"])
        for cust in customers
    ]

    cursor.executemany(customer_sql, customer_data)
    logger.info(f"✅ Inserted {len(customers)} customers")

    # Insert products
    product_sql = """
    INSERT INTO products (product_id, product_name, product_category, unit_price, cost_price)
    VALUES (:1, :2, :3, :4, :5)
    """

    product_data = [
        (prod["product_id"], prod["product_name"], prod["product_category"],
         prod["unit_price"], prod["cost_price"])
        for prod in products
    ]

    cursor.executemany(product_sql, product_data)
    logger.info(f"✅ Inserted {len(products)} products")
    
    # Generate and insert sales transactions
    transaction_sql = """
    INSERT INTO sales_transactions (transaction_id, customer_id, rep_id, product_id,
                                   transaction_date, quantity, unit_price, total_amount,
                                   discount_percent, transaction_type, status)
    VALUES (transaction_seq.NEXTVAL, :1, :2, :3, :4, :5, :6, :7, :8, :9, :10)
    """

    transaction_data = []
    for _ in range(200):  # Generate 200 transactions
        customer = random.choice(customers)
        rep = random.choice(sales_reps)
        product = random.choice(products)

        # Transaction date within last year
        days_ago = random.randint(1, 365)
        transaction_date = datetime.now() - timedelta(days=days_ago)

        quantity = random.randint(1, 10)
        unit_price = product["unit_price"]
        discount_percent = random.choice([0, 5, 10, 15, 20])

        total_amount = quantity * unit_price * (1 - discount_percent / 100)

        transaction_data.append((
            customer["customer_id"], rep["rep_id"], product["product_id"],
            transaction_date, quantity, unit_price, round(total_amount, 2),
            discount_percent, "Sale", "Completed"
        ))

    cursor.executemany(transaction_sql, transaction_data)
    logger.info(f"✅ Inserted {len(transaction_data)} sales transactions")

    # Generate and insert sales targets
    target_sql = """
    INSERT INTO sales_targets (target_id, rep_id, target_period, target_amount,
                              actual_amount, target_year, target_quarter, status)
    VALUES (target_seq.NEXTVAL, :1, :2, :3, :4, :5, :6, :7)
    """

    target_data = []
    current_year = datetime.now().year

    for rep in sales_reps:
        # Create quarterly targets for current year
        for quarter in range(1, 5):
            target_amount = random.randint(50000, 200000)
            actual_amount = random.randint(int(target_amount * 0.7), int(target_amount * 1.3))

            target_data.append((
                rep["rep_id"], f"Q{quarter} {current_year}", target_amount,
                actual_amount, current_year, quarter, "Active"
            ))

    cursor.executemany(target_sql, target_data)
    logger.info(f"✅ Inserted {len(target_data)} sales targets")

    # Generate and insert sales metrics
    metric_sql = """
    INSERT INTO sales_metrics (metric_id, rep_id, metric_name, metric_value,
                              metric_unit, measurement_date, metric_category, target_value)
    VALUES (metric_seq.NEXTVAL, :1, :2, :3, :4, :5, :6, :7)
    """

    metric_names = [
        "Conversion Rate", "Average Deal Size", "Sales Cycle Length",
        "Customer Acquisition Cost", "Lead Response Time", "Quota Attainment",
        "Pipeline Value", "Win Rate", "Customer Retention Rate"
    ]

    metric_data = []
    for rep in sales_reps[:30]:  # Metrics for first 30 sales reps
        num_metrics = random.randint(3, 6)
        for _ in range(num_metrics):
            metric_name = random.choice(metric_names)

            # Generate realistic metric values based on metric type
            if "Rate" in metric_name:
                value = round(random.uniform(15, 85), 2)
                unit = "percentage"
                target = 50.0
            elif "Size" in metric_name or "Cost" in metric_name or "Value" in metric_name:
                value = round(random.uniform(1000, 50000), 2)
                unit = "dollars"
                target = 25000.0
            elif "Time" in metric_name or "Length" in metric_name:
                value = round(random.uniform(1, 30), 2)
                unit = "days"
                target = 15.0
            else:
                value = round(random.uniform(50, 120), 2)
                unit = "percentage"
                target = 100.0

            measurement_date = datetime.now() - timedelta(days=random.randint(1, 90))
            category = random.choice(["Sales", "Performance", "Efficiency", "Quality"])

            metric_data.append((
                rep["rep_id"], metric_name, value, unit, measurement_date, category, target
            ))

    cursor.executemany(metric_sql, metric_data)
    logger.info(f"✅ Inserted {len(metric_data)} sales metrics")

    connection.commit()
    cursor.close()

    logger.info("🎉 Sample data inserted successfully!")

def create_oracle_sales_db():
    """Main function to create Oracle sales database"""
    
    if not ORACLE_AVAILABLE:
        logger.error("❌ Oracle database driver not available. Install oracledb package.")
        return False
    
    try:
        # Get database connection (as SYSTEM user for Docker)
        connection = get_oracle_connection()

        # Setup HR user if connected as SYSTEM
        username = os.getenv('ORACLE_USERNAME', 'system')
        if username.lower() == 'system':
            try:
                setup_hr_user(connection)

                # Close SYSTEM connection and reconnect as HR
                connection.close()
                logger.info("🔄 Reconnecting as HR user...")

                # Temporarily change environment for HR connection
                original_username = os.environ.get('ORACLE_USERNAME')
                original_password = os.environ.get('ORACLE_PASSWORD')

                os.environ['ORACLE_USERNAME'] = 'hr'
                os.environ['ORACLE_PASSWORD'] = 'hr'

                try:
                    connection = get_oracle_connection()
                    logger.info("✅ Connected as HR user")
                except Exception as hr_connect_error:
                    logger.error(f"❌ Failed to connect as HR user: {hr_connect_error}")
                    logger.info("🔄 Falling back to SYSTEM user for schema creation...")

                    # Restore original environment and reconnect as SYSTEM
                    if original_username:
                        os.environ['ORACLE_USERNAME'] = original_username
                    else:
                        os.environ.pop('ORACLE_USERNAME', None)

                    if original_password:
                        os.environ['ORACLE_PASSWORD'] = original_password
                    else:
                        os.environ.pop('ORACLE_PASSWORD', None)

                    connection = get_oracle_connection()
                    logger.info("✅ Reconnected as SYSTEM user")
                finally:
                    # Ensure environment is restored
                    if 'ORACLE_USERNAME' in os.environ and os.environ['ORACLE_USERNAME'] == 'hr':
                        if original_username:
                            os.environ['ORACLE_USERNAME'] = original_username
                        else:
                            os.environ.pop('ORACLE_USERNAME', None)

                    if 'ORACLE_PASSWORD' in os.environ and os.environ['ORACLE_PASSWORD'] == 'hr':
                        if original_password:
                            os.environ['ORACLE_PASSWORD'] = original_password
                        else:
                            os.environ.pop('ORACLE_PASSWORD', None)

            except Exception as setup_error:
                logger.error(f"❌ HR user setup failed: {setup_error}")
                logger.info("🔄 Continuing with SYSTEM user for schema creation...")
                # Continue with SYSTEM user

        # Create schema
        create_sales_database_schema(connection)

        # Generate and insert sample data
        sales_data = generate_sample_sales_data()
        insert_sample_data(connection, sales_data)
        
        # Close connection
        connection.close()
        
        logger.info("🎉 Oracle sales database created successfully!")
        logger.info(f"📊 Total sales reps: {len(sales_data['sales_reps'])}")
        logger.info(f"📊 Total customers: {len(sales_data['customers'])}")
        logger.info(f"📊 Total products: {len(sales_data['products'])}")
        logger.info("🔍 Sample queries you can try:")
        logger.info("   - Show all sales reps in North territory")
        logger.info("   - Find top performing sales reps by revenue")
        logger.info("   - List customers by industry")
        logger.info("   - Show sales targets vs actual by quarter")
        logger.info("   - Find best selling products")
        logger.info("   - Get sales metrics summary by territory")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create Oracle database: {e}")
        return False

if __name__ == "__main__":
    # Set up logging
    logger.add(sys.stdout, format="{time} | {level} | {message}", level="INFO")
    
    print("🏢 Oracle Sales Database Creator")
    print("=" * 50)
    print()
    print("📋 Environment Variables for Oracle Docker Container:")
    print("   ORACLE_HOST (default: localhost)")
    print("   ORACLE_PORT (default: 1521)")
    print("   ORACLE_SERVICE_NAME (default: FREEPDB1)")
    print("   ORACLE_USERNAME (default: system)")
    print("   ORACLE_PASSWORD (default: oracle123)")
    print()
    print("🐳 Docker Container Setup:")
    print("   docker run -d --name oracle-free -p 1521:1521 -e ORACLE_PWD=oracle123 \\")
    print("   container-registry.oracle.com/database/free:latest")
    print()
    
    success = create_oracle_sales_db()
    
    if success:
        print("\n✅ Database creation completed successfully!")
    else:
        print("\n❌ Database creation failed!")
        sys.exit(1)
