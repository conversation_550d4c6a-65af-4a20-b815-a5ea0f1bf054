{"version": 3, "sources": ["../../lodash.debounce/index.js", "../../react-speech-recognition/dist/cc-BU0zEyYq.js", "../../react-speech-recognition/dist/index.js"], "sourcesContent": ["/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as the `TypeError` message for \"Functions\" methods. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/** Used to match leading and trailing whitespace. */\nvar reTrim = /^\\s+|\\s+$/g;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        result = wait - timeSinceLastCall;\n\n    return maxing ? nativeMin(result, maxWait - timeSinceLastInvoke) : result;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\n}\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = value.replace(reTrim, '');\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nmodule.exports = debounce;\n", "function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n    try {\n        var info = gen[key](arg);\n        var value = info.value;\n    } catch (error) {\n        reject(error);\n        return;\n    }\n    if (info.done) resolve(value);\n    else Promise.resolve(value).then(_next, _throw);\n}\nfunction _async_to_generator(fn) {\n    return function() {\n        var self = this, args = arguments;\n        return new Promise(function(resolve, reject) {\n            var gen = fn.apply(self, args);\n            function _next(value) {\n                asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n            }\n            function _throw(err) {\n                asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n            }\n            _next(undefined);\n        });\n    };\n}\n\nexport { _async_to_generator as _ };\n", "import { _ as _async_to_generator } from './cc-BU0zEyYq.js';\nimport { useState, useReducer, useRef, useCallback, useEffect } from 'react';\nimport debounce from 'lodash.debounce';\n\nconst NativeSpeechRecognition = typeof window !== \"undefined\" && (window.SpeechRecognition || window.webkitSpeechRecognition || window.mozSpeechRecognition || window.msSpeechRecognition || window.oSpeechRecognition);\nconst isNative = (SpeechRecognition)=>SpeechRecognition === NativeSpeechRecognition;\n\nvar isAndroid = (()=>/(android)/i.test(typeof navigator !== \"undefined\" ? navigator.userAgent : \"\"));\n\nconst concatTranscripts = (...transcriptParts)=>{\n    return transcriptParts.map((t)=>t.trim()).join(\" \").trim();\n};\n// The command matching code is a modified version of Backbone.Router by <PERSON>, under the MIT license.\nconst optionalParam = /\\s*\\((.*?)\\)\\s*/g;\nconst optionalRegex = /(\\(\\?:[^)]+\\))\\?/g;\nconst namedParam = /(\\(\\?)?:\\w+/g;\nconst splatParam = /\\*/g;\nconst escapeRegExp = /[-{}[\\]+?.,\\\\^$|#]/g;\nconst commandToRegExp = (command)=>{\n    if (command instanceof RegExp) {\n        return new RegExp(command.source, \"i\");\n    }\n    command = command.replace(escapeRegExp, \"\\\\$&\").replace(optionalParam, \"(?:$1)?\").replace(namedParam, (match, optional)=>{\n        return optional ? match : \"([^\\\\s]+)\";\n    }).replace(splatParam, \"(.*?)\").replace(optionalRegex, \"\\\\s*$1?\\\\s*\");\n    return new RegExp(\"^\" + command + \"$\", \"i\");\n};\n// this is from https://github.com/aceakash/string-similarity\nconst compareTwoStringsUsingDiceCoefficient = (first, second)=>{\n    first = first.replace(/\\s+/g, \"\").toLowerCase();\n    second = second.replace(/\\s+/g, \"\").toLowerCase();\n    if (!first.length && !second.length) return 1; // if both are empty strings\n    if (!first.length || !second.length) return 0; // if only one is empty string\n    if (first === second) return 1; // identical\n    if (first.length === 1 && second.length === 1) return 0; // both are 1-letter strings\n    if (first.length < 2 || second.length < 2) return 0; // if either is a 1-letter string\n    const firstBigrams = new Map();\n    for(let i = 0; i < first.length - 1; i++){\n        const bigram = first.substring(i, i + 2);\n        const count = firstBigrams.has(bigram) ? firstBigrams.get(bigram) + 1 : 1;\n        firstBigrams.set(bigram, count);\n    }\n    let intersectionSize = 0;\n    for(let i = 0; i < second.length - 1; i++){\n        const bigram = second.substring(i, i + 2);\n        const count = firstBigrams.has(bigram) ? firstBigrams.get(bigram) : 0;\n        if (count > 0) {\n            firstBigrams.set(bigram, count - 1);\n            intersectionSize++;\n        }\n    }\n    return 2.0 * intersectionSize / (first.length + second.length - 2);\n};\nconst browserSupportsPolyfills = ()=>{\n    return typeof window !== \"undefined\" && window.navigator !== undefined && window.navigator.mediaDevices !== undefined && window.navigator.mediaDevices.getUserMedia !== undefined && (window.AudioContext !== undefined || window.webkitAudioContext !== undefined);\n};\n\nclass RecognitionManager {\n    setSpeechRecognition(SpeechRecognition) {\n        const browserSupportsRecogniser = !!SpeechRecognition && (isNative(SpeechRecognition) || browserSupportsPolyfills());\n        if (browserSupportsRecogniser) {\n            this.disableRecognition();\n            this.recognition = new SpeechRecognition();\n            this.recognition.continuous = false;\n            this.recognition.interimResults = true;\n            this.recognition.onresult = this.updateTranscript.bind(this);\n            this.recognition.onend = this.onRecognitionDisconnect.bind(this);\n            this.recognition.onerror = this.onError.bind(this);\n        }\n        this.emitBrowserSupportsSpeechRecognitionChange(browserSupportsRecogniser);\n    }\n    subscribe(id, callbacks) {\n        this.subscribers[id] = callbacks;\n    }\n    unsubscribe(id) {\n        delete this.subscribers[id];\n    }\n    emitListeningChange(listening) {\n        this.listening = listening;\n        Object.keys(this.subscribers).forEach((id)=>{\n            const { onListeningChange } = this.subscribers[id];\n            onListeningChange(listening);\n        });\n    }\n    emitMicrophoneAvailabilityChange(isMicrophoneAvailable) {\n        this.isMicrophoneAvailable = isMicrophoneAvailable;\n        Object.keys(this.subscribers).forEach((id)=>{\n            const { onMicrophoneAvailabilityChange } = this.subscribers[id];\n            onMicrophoneAvailabilityChange(isMicrophoneAvailable);\n        });\n    }\n    emitTranscriptChange(interimTranscript, finalTranscript) {\n        Object.keys(this.subscribers).forEach((id)=>{\n            const { onTranscriptChange } = this.subscribers[id];\n            onTranscriptChange(interimTranscript, finalTranscript);\n        });\n    }\n    emitClearTranscript() {\n        Object.keys(this.subscribers).forEach((id)=>{\n            const { onClearTranscript } = this.subscribers[id];\n            onClearTranscript();\n        });\n    }\n    emitBrowserSupportsSpeechRecognitionChange(browserSupportsSpeechRecognitionChange) {\n        Object.keys(this.subscribers).forEach((id)=>{\n            const { onBrowserSupportsSpeechRecognitionChange, onBrowserSupportsContinuousListeningChange } = this.subscribers[id];\n            onBrowserSupportsSpeechRecognitionChange(browserSupportsSpeechRecognitionChange);\n            onBrowserSupportsContinuousListeningChange(browserSupportsSpeechRecognitionChange);\n        });\n    }\n    disconnect(disconnectType) {\n        if (this.recognition && this.listening) {\n            switch(disconnectType){\n                case \"ABORT\":\n                    this.pauseAfterDisconnect = true;\n                    this.abort();\n                    break;\n                case \"RESET\":\n                    this.pauseAfterDisconnect = false;\n                    this.abort();\n                    break;\n                case \"STOP\":\n                default:\n                    this.pauseAfterDisconnect = true;\n                    this.stop();\n            }\n        }\n    }\n    disableRecognition() {\n        if (this.recognition) {\n            this.recognition.onresult = ()=>{};\n            this.recognition.onend = ()=>{};\n            this.recognition.onerror = ()=>{};\n            if (this.listening) {\n                this.stopListening();\n            }\n        }\n    }\n    onError(event) {\n        if (event && event.error && event.error === \"not-allowed\") {\n            this.emitMicrophoneAvailabilityChange(false);\n            this.disableRecognition();\n        }\n    }\n    onRecognitionDisconnect() {\n        this.onStopListening();\n        this.listening = false;\n        if (this.pauseAfterDisconnect) {\n            this.emitListeningChange(false);\n        } else if (this.recognition) {\n            if (this.recognition.continuous) {\n                this.startListening({\n                    continuous: this.recognition.continuous\n                });\n            } else {\n                this.emitListeningChange(false);\n            }\n        }\n        this.pauseAfterDisconnect = false;\n    }\n    updateTranscript({ results, resultIndex }) {\n        const currentIndex = resultIndex === undefined ? results.length - 1 : resultIndex;\n        this.interimTranscript = \"\";\n        this.finalTranscript = \"\";\n        for(let i = currentIndex; i < results.length; ++i){\n            if (results[i].isFinal && (!isAndroid() || results[i][0].confidence > 0)) {\n                this.updateFinalTranscript(results[i][0].transcript);\n            } else {\n                this.interimTranscript = concatTranscripts(this.interimTranscript, results[i][0].transcript);\n            }\n        }\n        let isDuplicateResult = false;\n        if (this.interimTranscript === \"\" && this.finalTranscript !== \"\") {\n            if (this.previousResultWasFinalOnly) {\n                isDuplicateResult = true;\n            }\n            this.previousResultWasFinalOnly = true;\n        } else {\n            this.previousResultWasFinalOnly = false;\n        }\n        if (!isDuplicateResult) {\n            this.emitTranscriptChange(this.interimTranscript, this.finalTranscript);\n        }\n    }\n    updateFinalTranscript(newFinalTranscript) {\n        this.finalTranscript = concatTranscripts(this.finalTranscript, newFinalTranscript);\n    }\n    resetTranscript() {\n        this.disconnect(\"RESET\");\n    }\n    startListening() {\n        return /*#__PURE__*/ _async_to_generator(function*({ continuous = false, language } = {}) {\n            if (!this.recognition) {\n                return;\n            }\n            const isContinuousChanged = continuous !== this.recognition.continuous;\n            const isLanguageChanged = language && language !== this.recognition.lang;\n            if (isContinuousChanged || isLanguageChanged) {\n                if (this.listening) {\n                    yield this.stopListening();\n                }\n                this.recognition.continuous = isContinuousChanged ? continuous : this.recognition.continuous;\n                this.recognition.lang = isLanguageChanged ? language : this.recognition.lang;\n            }\n            if (!this.listening) {\n                if (!this.recognition.continuous) {\n                    this.resetTranscript();\n                    this.emitClearTranscript();\n                }\n                try {\n                    yield this.start();\n                    this.emitListeningChange(true);\n                } catch (e) {\n                    // DOMExceptions indicate a redundant microphone start - safe to swallow\n                    if (!(e instanceof DOMException)) {\n                        this.emitMicrophoneAvailabilityChange(false);\n                    }\n                }\n            }\n        }).apply(this, arguments);\n    }\n    abortListening() {\n        return /*#__PURE__*/ _async_to_generator(function*() {\n            this.disconnect(\"ABORT\");\n            this.emitListeningChange(false);\n            yield new Promise((resolve)=>{\n                this.onStopListening = resolve;\n            });\n        }).call(this);\n    }\n    stopListening() {\n        return /*#__PURE__*/ _async_to_generator(function*() {\n            this.disconnect(\"STOP\");\n            this.emitListeningChange(false);\n            yield new Promise((resolve)=>{\n                this.onStopListening = resolve;\n            });\n        }).call(this);\n    }\n    getRecognition() {\n        return this.recognition;\n    }\n    start() {\n        return /*#__PURE__*/ _async_to_generator(function*() {\n            if (this.recognition && !this.listening) {\n                yield this.recognition.start();\n                this.listening = true;\n            }\n        }).call(this);\n    }\n    stop() {\n        if (this.recognition && this.listening) {\n            this.recognition.stop();\n            this.listening = false;\n        }\n    }\n    abort() {\n        if (this.recognition && this.listening) {\n            this.recognition.abort();\n            this.listening = false;\n        }\n    }\n    constructor(SpeechRecognition){\n        this.recognition = null;\n        this.pauseAfterDisconnect = false;\n        this.interimTranscript = \"\";\n        this.finalTranscript = \"\";\n        this.listening = false;\n        this.isMicrophoneAvailable = true;\n        this.subscribers = {};\n        this.onStopListening = ()=>{};\n        this.previousResultWasFinalOnly = false;\n        this.resetTranscript = this.resetTranscript.bind(this);\n        this.startListening = this.startListening.bind(this);\n        this.stopListening = this.stopListening.bind(this);\n        this.abortListening = this.abortListening.bind(this);\n        this.setSpeechRecognition = this.setSpeechRecognition.bind(this);\n        this.disableRecognition = this.disableRecognition.bind(this);\n        this.setSpeechRecognition(SpeechRecognition);\n        if (isAndroid()) {\n            this.updateFinalTranscript = debounce(this.updateFinalTranscript, 250, {\n                leading: true\n            });\n        }\n    }\n}\n\nconst CLEAR_TRANSCRIPT = \"CLEAR_TRANSCRIPT\";\nconst APPEND_TRANSCRIPT = \"APPEND_TRANSCRIPT\";\n\nconst clearTranscript = ()=>{\n    return {\n        type: CLEAR_TRANSCRIPT\n    };\n};\nconst appendTranscript = (interimTranscript, finalTranscript)=>{\n    return {\n        type: APPEND_TRANSCRIPT,\n        payload: {\n            interimTranscript,\n            finalTranscript\n        }\n    };\n};\n\nconst transcriptReducer = (state, action)=>{\n    switch(action.type){\n        case CLEAR_TRANSCRIPT:\n            return {\n                interimTranscript: \"\",\n                finalTranscript: \"\"\n            };\n        case APPEND_TRANSCRIPT:\n            return {\n                interimTranscript: action.payload.interimTranscript,\n                finalTranscript: concatTranscripts(state.finalTranscript, action.payload.finalTranscript)\n            };\n        default:\n            throw new Error();\n    }\n};\n\nlet _browserSupportsSpeechRecognition = !!NativeSpeechRecognition;\nlet _browserSupportsContinuousListening = _browserSupportsSpeechRecognition && !isAndroid();\nlet recognitionManager;\nconst useSpeechRecognition = ({ transcribing = true, clearTranscriptOnListen = true, commands = [] } = {})=>{\n    const [recognitionManager] = useState(SpeechRecognition.getRecognitionManager());\n    const [browserSupportsSpeechRecognition, setBrowserSupportsSpeechRecognition] = useState(_browserSupportsSpeechRecognition);\n    const [browserSupportsContinuousListening, setBrowserSupportsContinuousListening] = useState(_browserSupportsContinuousListening);\n    const [{ interimTranscript, finalTranscript }, dispatch] = useReducer(transcriptReducer, {\n        interimTranscript: recognitionManager.interimTranscript,\n        finalTranscript: \"\"\n    });\n    const [listening, setListening] = useState(recognitionManager.listening);\n    const [isMicrophoneAvailable, setMicrophoneAvailable] = useState(recognitionManager.isMicrophoneAvailable);\n    const commandsRef = useRef(commands);\n    commandsRef.current = commands;\n    const dispatchClearTranscript = ()=>{\n        dispatch(clearTranscript());\n    };\n    const resetTranscript = useCallback(()=>{\n        recognitionManager.resetTranscript();\n        dispatchClearTranscript();\n    }, [\n        recognitionManager\n    ]);\n    const testFuzzyMatch = (command, input, fuzzyMatchingThreshold)=>{\n        const commandToString = typeof command === \"object\" ? command.toString() : command;\n        const commandWithoutSpecials = commandToString.replace(/[&/\\\\#,+()!$~%.'\":*?<>{}]/g, \"\").replace(/  +/g, \" \").trim();\n        const howSimilar = compareTwoStringsUsingDiceCoefficient(commandWithoutSpecials, input);\n        if (howSimilar >= fuzzyMatchingThreshold) {\n            return {\n                command,\n                commandWithoutSpecials,\n                howSimilar,\n                isFuzzyMatch: true\n            };\n        }\n        return null;\n    };\n    const testMatch = (command, input)=>{\n        const pattern = commandToRegExp(command);\n        const result = pattern.exec(input);\n        if (result) {\n            return {\n                command,\n                parameters: result.slice(1)\n            };\n        }\n        return null;\n    };\n    const matchCommands = useCallback((newInterimTranscript, newFinalTranscript)=>{\n        commandsRef.current.forEach(({ command, callback, matchInterim = false, isFuzzyMatch = false, fuzzyMatchingThreshold = 0.8, bestMatchOnly = false })=>{\n            const input = !newFinalTranscript && matchInterim ? newInterimTranscript.trim() : newFinalTranscript.trim();\n            const subcommands = Array.isArray(command) ? command : [\n                command\n            ];\n            const results = subcommands.map((subcommand)=>{\n                if (isFuzzyMatch) {\n                    return testFuzzyMatch(subcommand, input, fuzzyMatchingThreshold);\n                }\n                return testMatch(subcommand, input);\n            }).filter((x)=>x);\n            if (isFuzzyMatch && bestMatchOnly && results.length >= 2) {\n                results.sort((a, b)=>b.howSimilar - a.howSimilar);\n                const { command, commandWithoutSpecials, howSimilar } = results[0];\n                callback(commandWithoutSpecials, input, howSimilar, {\n                    command,\n                    resetTranscript\n                });\n            } else {\n                results.forEach((result)=>{\n                    if (result.isFuzzyMatch) {\n                        const { command, commandWithoutSpecials, howSimilar } = result;\n                        callback(commandWithoutSpecials, input, howSimilar, {\n                            command,\n                            resetTranscript\n                        });\n                    } else {\n                        const { command, parameters } = result;\n                        callback(...parameters, {\n                            command,\n                            resetTranscript\n                        });\n                    }\n                });\n            }\n        });\n    }, [\n        resetTranscript\n    ]);\n    const handleTranscriptChange = useCallback((newInterimTranscript, newFinalTranscript)=>{\n        if (transcribing) {\n            dispatch(appendTranscript(newInterimTranscript, newFinalTranscript));\n        }\n        matchCommands(newInterimTranscript, newFinalTranscript);\n    }, [\n        matchCommands,\n        transcribing\n    ]);\n    const handleClearTranscript = useCallback(()=>{\n        if (clearTranscriptOnListen) {\n            dispatchClearTranscript();\n        }\n    }, [\n        clearTranscriptOnListen\n    ]);\n    useEffect(()=>{\n        const id = SpeechRecognition.counter;\n        SpeechRecognition.counter += 1;\n        const callbacks = {\n            onListeningChange: setListening,\n            onMicrophoneAvailabilityChange: setMicrophoneAvailable,\n            onTranscriptChange: handleTranscriptChange,\n            onClearTranscript: handleClearTranscript,\n            onBrowserSupportsSpeechRecognitionChange: setBrowserSupportsSpeechRecognition,\n            onBrowserSupportsContinuousListeningChange: setBrowserSupportsContinuousListening\n        };\n        recognitionManager.subscribe(id, callbacks);\n        return ()=>{\n            recognitionManager.unsubscribe(id);\n        };\n    }, [\n        transcribing,\n        clearTranscriptOnListen,\n        recognitionManager,\n        handleTranscriptChange,\n        handleClearTranscript\n    ]);\n    const transcript = concatTranscripts(finalTranscript, interimTranscript);\n    return {\n        transcript,\n        interimTranscript,\n        finalTranscript,\n        listening,\n        isMicrophoneAvailable,\n        resetTranscript,\n        browserSupportsSpeechRecognition,\n        browserSupportsContinuousListening\n    };\n};\nconst SpeechRecognition = {\n    counter: 0,\n    applyPolyfill: (PolyfillSpeechRecognition)=>{\n        if (recognitionManager) {\n            recognitionManager.setSpeechRecognition(PolyfillSpeechRecognition);\n        } else {\n            recognitionManager = new RecognitionManager(PolyfillSpeechRecognition);\n        }\n        const browserSupportsPolyfill = !!PolyfillSpeechRecognition && browserSupportsPolyfills();\n        _browserSupportsSpeechRecognition = browserSupportsPolyfill;\n        _browserSupportsContinuousListening = browserSupportsPolyfill;\n    },\n    removePolyfill: ()=>{\n        if (recognitionManager) {\n            recognitionManager.setSpeechRecognition(NativeSpeechRecognition);\n        } else {\n            recognitionManager = new RecognitionManager(NativeSpeechRecognition);\n        }\n        _browserSupportsSpeechRecognition = !!NativeSpeechRecognition;\n        _browserSupportsContinuousListening = _browserSupportsSpeechRecognition && !isAndroid();\n    },\n    getRecognitionManager: ()=>{\n        if (!recognitionManager) {\n            recognitionManager = new RecognitionManager(NativeSpeechRecognition);\n        }\n        return recognitionManager;\n    },\n    getRecognition: ()=>{\n        const recognitionManager = SpeechRecognition.getRecognitionManager();\n        return recognitionManager.getRecognition();\n    },\n    startListening: ({ continuous, language } = {})=>/*#__PURE__*/ _async_to_generator(function*() {\n            const recognitionManager = SpeechRecognition.getRecognitionManager();\n            yield recognitionManager.startListening({\n                continuous,\n                language\n            });\n        })(),\n    stopListening: ()=>/*#__PURE__*/ _async_to_generator(function*() {\n            const recognitionManager = SpeechRecognition.getRecognitionManager();\n            yield recognitionManager.stopListening();\n        })(),\n    abortListening: ()=>/*#__PURE__*/ _async_to_generator(function*() {\n            const recognitionManager = SpeechRecognition.getRecognitionManager();\n            yield recognitionManager.abortListening();\n        })(),\n    browserSupportsSpeechRecognition: ()=>_browserSupportsSpeechRecognition,\n    browserSupportsContinuousListening: ()=>_browserSupportsContinuousListening\n};\n\nexport { SpeechRecognition as default, useSpeechRecognition };\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAUA,QAAI,kBAAkB;AAGtB,QAAI,MAAM,IAAI;AAGd,QAAI,YAAY;AAGhB,QAAI,SAAS;AAGb,QAAI,aAAa;AAGjB,QAAI,aAAa;AAGjB,QAAI,YAAY;AAGhB,QAAI,eAAe;AAGnB,QAAI,aAAa,OAAO,UAAU,YAAY,UAAU,OAAO,WAAW,UAAU;AAGpF,QAAI,WAAW,OAAO,QAAQ,YAAY,QAAQ,KAAK,WAAW,UAAU;AAG5E,QAAI,OAAO,cAAc,YAAY,SAAS,aAAa,EAAE;AAG7D,QAAI,cAAc,OAAO;AAOzB,QAAI,iBAAiB,YAAY;AAGjC,QAAI,YAAY,KAAK;AAArB,QACI,YAAY,KAAK;AAkBrB,QAAI,MAAM,WAAW;AACnB,aAAO,KAAK,KAAK,IAAI;AAAA,IACvB;AAwDA,aAASA,UAAS,MAAM,MAAM,SAAS;AACrC,UAAI,UACA,UACA,SACA,QACA,SACA,cACA,iBAAiB,GACjB,UAAU,OACV,SAAS,OACT,WAAW;AAEf,UAAI,OAAO,QAAQ,YAAY;AAC7B,cAAM,IAAI,UAAU,eAAe;AAAA,MACrC;AACA,aAAO,SAAS,IAAI,KAAK;AACzB,UAAI,SAAS,OAAO,GAAG;AACrB,kBAAU,CAAC,CAAC,QAAQ;AACpB,iBAAS,aAAa;AACtB,kBAAU,SAAS,UAAU,SAAS,QAAQ,OAAO,KAAK,GAAG,IAAI,IAAI;AACrE,mBAAW,cAAc,UAAU,CAAC,CAAC,QAAQ,WAAW;AAAA,MAC1D;AAEA,eAAS,WAAW,MAAM;AACxB,YAAI,OAAO,UACP,UAAU;AAEd,mBAAW,WAAW;AACtB,yBAAiB;AACjB,iBAAS,KAAK,MAAM,SAAS,IAAI;AACjC,eAAO;AAAA,MACT;AAEA,eAAS,YAAY,MAAM;AAEzB,yBAAiB;AAEjB,kBAAU,WAAW,cAAc,IAAI;AAEvC,eAAO,UAAU,WAAW,IAAI,IAAI;AAAA,MACtC;AAEA,eAAS,cAAc,MAAM;AAC3B,YAAI,oBAAoB,OAAO,cAC3B,sBAAsB,OAAO,gBAC7BC,UAAS,OAAO;AAEpB,eAAO,SAAS,UAAUA,SAAQ,UAAU,mBAAmB,IAAIA;AAAA,MACrE;AAEA,eAAS,aAAa,MAAM;AAC1B,YAAI,oBAAoB,OAAO,cAC3B,sBAAsB,OAAO;AAKjC,eAAQ,iBAAiB,UAAc,qBAAqB,QACzD,oBAAoB,KAAO,UAAU,uBAAuB;AAAA,MACjE;AAEA,eAAS,eAAe;AACtB,YAAI,OAAO,IAAI;AACf,YAAI,aAAa,IAAI,GAAG;AACtB,iBAAO,aAAa,IAAI;AAAA,QAC1B;AAEA,kBAAU,WAAW,cAAc,cAAc,IAAI,CAAC;AAAA,MACxD;AAEA,eAAS,aAAa,MAAM;AAC1B,kBAAU;AAIV,YAAI,YAAY,UAAU;AACxB,iBAAO,WAAW,IAAI;AAAA,QACxB;AACA,mBAAW,WAAW;AACtB,eAAO;AAAA,MACT;AAEA,eAAS,SAAS;AAChB,YAAI,YAAY,QAAW;AACzB,uBAAa,OAAO;AAAA,QACtB;AACA,yBAAiB;AACjB,mBAAW,eAAe,WAAW,UAAU;AAAA,MACjD;AAEA,eAAS,QAAQ;AACf,eAAO,YAAY,SAAY,SAAS,aAAa,IAAI,CAAC;AAAA,MAC5D;AAEA,eAAS,YAAY;AACnB,YAAI,OAAO,IAAI,GACX,aAAa,aAAa,IAAI;AAElC,mBAAW;AACX,mBAAW;AACX,uBAAe;AAEf,YAAI,YAAY;AACd,cAAI,YAAY,QAAW;AACzB,mBAAO,YAAY,YAAY;AAAA,UACjC;AACA,cAAI,QAAQ;AAEV,sBAAU,WAAW,cAAc,IAAI;AACvC,mBAAO,WAAW,YAAY;AAAA,UAChC;AAAA,QACF;AACA,YAAI,YAAY,QAAW;AACzB,oBAAU,WAAW,cAAc,IAAI;AAAA,QACzC;AACA,eAAO;AAAA,MACT;AACA,gBAAU,SAAS;AACnB,gBAAU,QAAQ;AAClB,aAAO;AAAA,IACT;AA2BA,aAAS,SAAS,OAAO;AACvB,UAAI,OAAO,OAAO;AAClB,aAAO,CAAC,CAAC,UAAU,QAAQ,YAAY,QAAQ;AAAA,IACjD;AA0BA,aAAS,aAAa,OAAO;AAC3B,aAAO,CAAC,CAAC,SAAS,OAAO,SAAS;AAAA,IACpC;AAmBA,aAAS,SAAS,OAAO;AACvB,aAAO,OAAO,SAAS,YACpB,aAAa,KAAK,KAAK,eAAe,KAAK,KAAK,KAAK;AAAA,IAC1D;AAyBA,aAAS,SAAS,OAAO;AACvB,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO;AAAA,MACT;AACA,UAAI,SAAS,KAAK,GAAG;AACnB,eAAO;AAAA,MACT;AACA,UAAI,SAAS,KAAK,GAAG;AACnB,YAAI,QAAQ,OAAO,MAAM,WAAW,aAAa,MAAM,QAAQ,IAAI;AACnE,gBAAQ,SAAS,KAAK,IAAK,QAAQ,KAAM;AAAA,MAC3C;AACA,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO,UAAU,IAAI,QAAQ,CAAC;AAAA,MAChC;AACA,cAAQ,MAAM,QAAQ,QAAQ,EAAE;AAChC,UAAI,WAAW,WAAW,KAAK,KAAK;AACpC,aAAQ,YAAY,UAAU,KAAK,KAAK,IACpC,aAAa,MAAM,MAAM,CAAC,GAAG,WAAW,IAAI,CAAC,IAC5C,WAAW,KAAK,KAAK,IAAI,MAAM,CAAC;AAAA,IACvC;AAEA,WAAO,UAAUD;AAAA;AAAA;;;ACxXjB,SAAS,mBAAmB,KAAK,SAAS,QAAQ,OAAO,QAAQ,KAAK,KAAK;AACvE,MAAI;AACA,QAAI,OAAO,IAAI,GAAG,EAAE,GAAG;AACvB,QAAI,QAAQ,KAAK;AAAA,EACrB,SAAS,OAAO;AACZ,WAAO,KAAK;AACZ;AAAA,EACJ;AACA,MAAI,KAAK;AAAM,YAAQ,KAAK;AAAA;AACvB,YAAQ,QAAQ,KAAK,EAAE,KAAK,OAAO,MAAM;AAClD;AACA,SAAS,oBAAoB,IAAI;AAC7B,SAAO,WAAW;AACd,QAAIE,QAAO,MAAM,OAAO;AACxB,WAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AACzC,UAAI,MAAM,GAAG,MAAMA,OAAM,IAAI;AAC7B,eAAS,MAAM,OAAO;AAClB,2BAAmB,KAAK,SAAS,QAAQ,OAAO,QAAQ,QAAQ,KAAK;AAAA,MACzE;AACA,eAAS,OAAO,KAAK;AACjB,2BAAmB,KAAK,SAAS,QAAQ,OAAO,QAAQ,SAAS,GAAG;AAAA,MACxE;AACA,YAAM,MAAS;AAAA,IACnB,CAAC;AAAA,EACL;AACJ;;;ACxBA,mBAAqE;AACrE,oBAAqB;AAErB,IAAM,0BAA0B,OAAO,WAAW,gBAAgB,OAAO,qBAAqB,OAAO,2BAA2B,OAAO,wBAAwB,OAAO,uBAAuB,OAAO;AACpM,IAAM,WAAW,CAACC,uBAAoBA,uBAAsB;AAE5D,IAAI,YAAa,MAAI,aAAa,KAAK,OAAO,cAAc,cAAc,UAAU,YAAY,EAAE;AAElG,IAAM,oBAAoB,IAAI,oBAAkB;AAC5C,SAAO,gBAAgB,IAAI,CAAC,MAAI,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,EAAE,KAAK;AAC7D;AAEA,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AACtB,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAM,eAAe;AACrB,IAAM,kBAAkB,CAAC,YAAU;AAC/B,MAAI,mBAAmB,QAAQ;AAC3B,WAAO,IAAI,OAAO,QAAQ,QAAQ,GAAG;AAAA,EACzC;AACA,YAAU,QAAQ,QAAQ,cAAc,MAAM,EAAE,QAAQ,eAAe,SAAS,EAAE,QAAQ,YAAY,CAAC,OAAO,aAAW;AACrH,WAAO,WAAW,QAAQ;AAAA,EAC9B,CAAC,EAAE,QAAQ,YAAY,OAAO,EAAE,QAAQ,eAAe,aAAa;AACpE,SAAO,IAAI,OAAO,MAAM,UAAU,KAAK,GAAG;AAC9C;AAEA,IAAM,wCAAwC,CAAC,OAAO,WAAS;AAC3D,UAAQ,MAAM,QAAQ,QAAQ,EAAE,EAAE,YAAY;AAC9C,WAAS,OAAO,QAAQ,QAAQ,EAAE,EAAE,YAAY;AAChD,MAAI,CAAC,MAAM,UAAU,CAAC,OAAO;AAAQ,WAAO;AAC5C,MAAI,CAAC,MAAM,UAAU,CAAC,OAAO;AAAQ,WAAO;AAC5C,MAAI,UAAU;AAAQ,WAAO;AAC7B,MAAI,MAAM,WAAW,KAAK,OAAO,WAAW;AAAG,WAAO;AACtD,MAAI,MAAM,SAAS,KAAK,OAAO,SAAS;AAAG,WAAO;AAClD,QAAM,eAAe,oBAAI,IAAI;AAC7B,WAAQ,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,KAAI;AACrC,UAAM,SAAS,MAAM,UAAU,GAAG,IAAI,CAAC;AACvC,UAAM,QAAQ,aAAa,IAAI,MAAM,IAAI,aAAa,IAAI,MAAM,IAAI,IAAI;AACxE,iBAAa,IAAI,QAAQ,KAAK;AAAA,EAClC;AACA,MAAI,mBAAmB;AACvB,WAAQ,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,KAAI;AACtC,UAAM,SAAS,OAAO,UAAU,GAAG,IAAI,CAAC;AACxC,UAAM,QAAQ,aAAa,IAAI,MAAM,IAAI,aAAa,IAAI,MAAM,IAAI;AACpE,QAAI,QAAQ,GAAG;AACX,mBAAa,IAAI,QAAQ,QAAQ,CAAC;AAClC;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,IAAM,oBAAoB,MAAM,SAAS,OAAO,SAAS;AACpE;AACA,IAAM,2BAA2B,MAAI;AACjC,SAAO,OAAO,WAAW,eAAe,OAAO,cAAc,UAAa,OAAO,UAAU,iBAAiB,UAAa,OAAO,UAAU,aAAa,iBAAiB,WAAc,OAAO,iBAAiB,UAAa,OAAO,uBAAuB;AAC7P;AAEA,IAAM,qBAAN,MAAyB;AAAA,EACrB,qBAAqBA,oBAAmB;AACpC,UAAM,4BAA4B,CAAC,CAACA,uBAAsB,SAASA,kBAAiB,KAAK,yBAAyB;AAClH,QAAI,2BAA2B;AAC3B,WAAK,mBAAmB;AACxB,WAAK,cAAc,IAAIA,mBAAkB;AACzC,WAAK,YAAY,aAAa;AAC9B,WAAK,YAAY,iBAAiB;AAClC,WAAK,YAAY,WAAW,KAAK,iBAAiB,KAAK,IAAI;AAC3D,WAAK,YAAY,QAAQ,KAAK,wBAAwB,KAAK,IAAI;AAC/D,WAAK,YAAY,UAAU,KAAK,QAAQ,KAAK,IAAI;AAAA,IACrD;AACA,SAAK,2CAA2C,yBAAyB;AAAA,EAC7E;AAAA,EACA,UAAU,IAAI,WAAW;AACrB,SAAK,YAAY,EAAE,IAAI;AAAA,EAC3B;AAAA,EACA,YAAY,IAAI;AACZ,WAAO,KAAK,YAAY,EAAE;AAAA,EAC9B;AAAA,EACA,oBAAoB,WAAW;AAC3B,SAAK,YAAY;AACjB,WAAO,KAAK,KAAK,WAAW,EAAE,QAAQ,CAAC,OAAK;AACxC,YAAM,EAAE,kBAAkB,IAAI,KAAK,YAAY,EAAE;AACjD,wBAAkB,SAAS;AAAA,IAC/B,CAAC;AAAA,EACL;AAAA,EACA,iCAAiC,uBAAuB;AACpD,SAAK,wBAAwB;AAC7B,WAAO,KAAK,KAAK,WAAW,EAAE,QAAQ,CAAC,OAAK;AACxC,YAAM,EAAE,+BAA+B,IAAI,KAAK,YAAY,EAAE;AAC9D,qCAA+B,qBAAqB;AAAA,IACxD,CAAC;AAAA,EACL;AAAA,EACA,qBAAqB,mBAAmB,iBAAiB;AACrD,WAAO,KAAK,KAAK,WAAW,EAAE,QAAQ,CAAC,OAAK;AACxC,YAAM,EAAE,mBAAmB,IAAI,KAAK,YAAY,EAAE;AAClD,yBAAmB,mBAAmB,eAAe;AAAA,IACzD,CAAC;AAAA,EACL;AAAA,EACA,sBAAsB;AAClB,WAAO,KAAK,KAAK,WAAW,EAAE,QAAQ,CAAC,OAAK;AACxC,YAAM,EAAE,kBAAkB,IAAI,KAAK,YAAY,EAAE;AACjD,wBAAkB;AAAA,IACtB,CAAC;AAAA,EACL;AAAA,EACA,2CAA2C,wCAAwC;AAC/E,WAAO,KAAK,KAAK,WAAW,EAAE,QAAQ,CAAC,OAAK;AACxC,YAAM,EAAE,0CAA0C,2CAA2C,IAAI,KAAK,YAAY,EAAE;AACpH,+CAAyC,sCAAsC;AAC/E,iDAA2C,sCAAsC;AAAA,IACrF,CAAC;AAAA,EACL;AAAA,EACA,WAAW,gBAAgB;AACvB,QAAI,KAAK,eAAe,KAAK,WAAW;AACpC,cAAO,gBAAe;AAAA,QAClB,KAAK;AACD,eAAK,uBAAuB;AAC5B,eAAK,MAAM;AACX;AAAA,QACJ,KAAK;AACD,eAAK,uBAAuB;AAC5B,eAAK,MAAM;AACX;AAAA,QACJ,KAAK;AAAA,QACL;AACI,eAAK,uBAAuB;AAC5B,eAAK,KAAK;AAAA,MAClB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,qBAAqB;AACjB,QAAI,KAAK,aAAa;AAClB,WAAK,YAAY,WAAW,MAAI;AAAA,MAAC;AACjC,WAAK,YAAY,QAAQ,MAAI;AAAA,MAAC;AAC9B,WAAK,YAAY,UAAU,MAAI;AAAA,MAAC;AAChC,UAAI,KAAK,WAAW;AAChB,aAAK,cAAc;AAAA,MACvB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,QAAQ,OAAO;AACX,QAAI,SAAS,MAAM,SAAS,MAAM,UAAU,eAAe;AACvD,WAAK,iCAAiC,KAAK;AAC3C,WAAK,mBAAmB;AAAA,IAC5B;AAAA,EACJ;AAAA,EACA,0BAA0B;AACtB,SAAK,gBAAgB;AACrB,SAAK,YAAY;AACjB,QAAI,KAAK,sBAAsB;AAC3B,WAAK,oBAAoB,KAAK;AAAA,IAClC,WAAW,KAAK,aAAa;AACzB,UAAI,KAAK,YAAY,YAAY;AAC7B,aAAK,eAAe;AAAA,UAChB,YAAY,KAAK,YAAY;AAAA,QACjC,CAAC;AAAA,MACL,OAAO;AACH,aAAK,oBAAoB,KAAK;AAAA,MAClC;AAAA,IACJ;AACA,SAAK,uBAAuB;AAAA,EAChC;AAAA,EACA,iBAAiB,EAAE,SAAS,YAAY,GAAG;AACvC,UAAM,eAAe,gBAAgB,SAAY,QAAQ,SAAS,IAAI;AACtE,SAAK,oBAAoB;AACzB,SAAK,kBAAkB;AACvB,aAAQ,IAAI,cAAc,IAAI,QAAQ,QAAQ,EAAE,GAAE;AAC9C,UAAI,QAAQ,CAAC,EAAE,YAAY,CAAC,UAAU,KAAK,QAAQ,CAAC,EAAE,CAAC,EAAE,aAAa,IAAI;AACtE,aAAK,sBAAsB,QAAQ,CAAC,EAAE,CAAC,EAAE,UAAU;AAAA,MACvD,OAAO;AACH,aAAK,oBAAoB,kBAAkB,KAAK,mBAAmB,QAAQ,CAAC,EAAE,CAAC,EAAE,UAAU;AAAA,MAC/F;AAAA,IACJ;AACA,QAAI,oBAAoB;AACxB,QAAI,KAAK,sBAAsB,MAAM,KAAK,oBAAoB,IAAI;AAC9D,UAAI,KAAK,4BAA4B;AACjC,4BAAoB;AAAA,MACxB;AACA,WAAK,6BAA6B;AAAA,IACtC,OAAO;AACH,WAAK,6BAA6B;AAAA,IACtC;AACA,QAAI,CAAC,mBAAmB;AACpB,WAAK,qBAAqB,KAAK,mBAAmB,KAAK,eAAe;AAAA,IAC1E;AAAA,EACJ;AAAA,EACA,sBAAsB,oBAAoB;AACtC,SAAK,kBAAkB,kBAAkB,KAAK,iBAAiB,kBAAkB;AAAA,EACrF;AAAA,EACA,kBAAkB;AACd,SAAK,WAAW,OAAO;AAAA,EAC3B;AAAA,EACA,iBAAiB;AACb,WAAqB,oBAAoB,WAAU,EAAE,aAAa,OAAO,SAAS,IAAI,CAAC,GAAG;AACtF,UAAI,CAAC,KAAK,aAAa;AACnB;AAAA,MACJ;AACA,YAAM,sBAAsB,eAAe,KAAK,YAAY;AAC5D,YAAM,oBAAoB,YAAY,aAAa,KAAK,YAAY;AACpE,UAAI,uBAAuB,mBAAmB;AAC1C,YAAI,KAAK,WAAW;AAChB,gBAAM,KAAK,cAAc;AAAA,QAC7B;AACA,aAAK,YAAY,aAAa,sBAAsB,aAAa,KAAK,YAAY;AAClF,aAAK,YAAY,OAAO,oBAAoB,WAAW,KAAK,YAAY;AAAA,MAC5E;AACA,UAAI,CAAC,KAAK,WAAW;AACjB,YAAI,CAAC,KAAK,YAAY,YAAY;AAC9B,eAAK,gBAAgB;AACrB,eAAK,oBAAoB;AAAA,QAC7B;AACA,YAAI;AACA,gBAAM,KAAK,MAAM;AACjB,eAAK,oBAAoB,IAAI;AAAA,QACjC,SAAS,GAAG;AAER,cAAI,EAAE,aAAa,eAAe;AAC9B,iBAAK,iCAAiC,KAAK;AAAA,UAC/C;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC,EAAE,MAAM,MAAM,SAAS;AAAA,EAC5B;AAAA,EACA,iBAAiB;AACb,WAAqB,oBAAoB,aAAY;AACjD,WAAK,WAAW,OAAO;AACvB,WAAK,oBAAoB,KAAK;AAC9B,YAAM,IAAI,QAAQ,CAAC,YAAU;AACzB,aAAK,kBAAkB;AAAA,MAC3B,CAAC;AAAA,IACL,CAAC,EAAE,KAAK,IAAI;AAAA,EAChB;AAAA,EACA,gBAAgB;AACZ,WAAqB,oBAAoB,aAAY;AACjD,WAAK,WAAW,MAAM;AACtB,WAAK,oBAAoB,KAAK;AAC9B,YAAM,IAAI,QAAQ,CAAC,YAAU;AACzB,aAAK,kBAAkB;AAAA,MAC3B,CAAC;AAAA,IACL,CAAC,EAAE,KAAK,IAAI;AAAA,EAChB;AAAA,EACA,iBAAiB;AACb,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,QAAQ;AACJ,WAAqB,oBAAoB,aAAY;AACjD,UAAI,KAAK,eAAe,CAAC,KAAK,WAAW;AACrC,cAAM,KAAK,YAAY,MAAM;AAC7B,aAAK,YAAY;AAAA,MACrB;AAAA,IACJ,CAAC,EAAE,KAAK,IAAI;AAAA,EAChB;AAAA,EACA,OAAO;AACH,QAAI,KAAK,eAAe,KAAK,WAAW;AACpC,WAAK,YAAY,KAAK;AACtB,WAAK,YAAY;AAAA,IACrB;AAAA,EACJ;AAAA,EACA,QAAQ;AACJ,QAAI,KAAK,eAAe,KAAK,WAAW;AACpC,WAAK,YAAY,MAAM;AACvB,WAAK,YAAY;AAAA,IACrB;AAAA,EACJ;AAAA,EACA,YAAYA,oBAAkB;AAC1B,SAAK,cAAc;AACnB,SAAK,uBAAuB;AAC5B,SAAK,oBAAoB;AACzB,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,wBAAwB;AAC7B,SAAK,cAAc,CAAC;AACpB,SAAK,kBAAkB,MAAI;AAAA,IAAC;AAC5B,SAAK,6BAA6B;AAClC,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI;AACrD,SAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AACnD,SAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AACjD,SAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AACnD,SAAK,uBAAuB,KAAK,qBAAqB,KAAK,IAAI;AAC/D,SAAK,qBAAqB,KAAK,mBAAmB,KAAK,IAAI;AAC3D,SAAK,qBAAqBA,kBAAiB;AAC3C,QAAI,UAAU,GAAG;AACb,WAAK,4BAAwB,cAAAC,SAAS,KAAK,uBAAuB,KAAK;AAAA,QACnE,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;AAEA,IAAM,mBAAmB;AACzB,IAAM,oBAAoB;AAE1B,IAAM,kBAAkB,MAAI;AACxB,SAAO;AAAA,IACH,MAAM;AAAA,EACV;AACJ;AACA,IAAM,mBAAmB,CAAC,mBAAmB,oBAAkB;AAC3D,SAAO;AAAA,IACH,MAAM;AAAA,IACN,SAAS;AAAA,MACL;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,IAAM,oBAAoB,CAAC,OAAO,WAAS;AACvC,UAAO,OAAO,MAAK;AAAA,IACf,KAAK;AACD,aAAO;AAAA,QACH,mBAAmB;AAAA,QACnB,iBAAiB;AAAA,MACrB;AAAA,IACJ,KAAK;AACD,aAAO;AAAA,QACH,mBAAmB,OAAO,QAAQ;AAAA,QAClC,iBAAiB,kBAAkB,MAAM,iBAAiB,OAAO,QAAQ,eAAe;AAAA,MAC5F;AAAA,IACJ;AACI,YAAM,IAAI,MAAM;AAAA,EACxB;AACJ;AAEA,IAAI,oCAAoC,CAAC,CAAC;AAC1C,IAAI,sCAAsC,qCAAqC,CAAC,UAAU;AAC1F,IAAI;AACJ,IAAM,uBAAuB,CAAC,EAAE,eAAe,MAAM,0BAA0B,MAAM,WAAW,CAAC,EAAE,IAAI,CAAC,MAAI;AACxG,QAAM,CAACC,mBAAkB,QAAI,uBAAS,kBAAkB,sBAAsB,CAAC;AAC/E,QAAM,CAAC,kCAAkC,mCAAmC,QAAI,uBAAS,iCAAiC;AAC1H,QAAM,CAAC,oCAAoC,qCAAqC,QAAI,uBAAS,mCAAmC;AAChI,QAAM,CAAC,EAAE,mBAAmB,gBAAgB,GAAG,QAAQ,QAAI,yBAAW,mBAAmB;AAAA,IACrF,mBAAmBA,oBAAmB;AAAA,IACtC,iBAAiB;AAAA,EACrB,CAAC;AACD,QAAM,CAAC,WAAW,YAAY,QAAI,uBAASA,oBAAmB,SAAS;AACvE,QAAM,CAAC,uBAAuB,sBAAsB,QAAI,uBAASA,oBAAmB,qBAAqB;AACzG,QAAM,kBAAc,qBAAO,QAAQ;AACnC,cAAY,UAAU;AACtB,QAAM,0BAA0B,MAAI;AAChC,aAAS,gBAAgB,CAAC;AAAA,EAC9B;AACA,QAAM,sBAAkB,0BAAY,MAAI;AACpC,IAAAA,oBAAmB,gBAAgB;AACnC,4BAAwB;AAAA,EAC5B,GAAG;AAAA,IACCA;AAAA,EACJ,CAAC;AACD,QAAM,iBAAiB,CAAC,SAAS,OAAO,2BAAyB;AAC7D,UAAM,kBAAkB,OAAO,YAAY,WAAW,QAAQ,SAAS,IAAI;AAC3E,UAAM,yBAAyB,gBAAgB,QAAQ,8BAA8B,EAAE,EAAE,QAAQ,QAAQ,GAAG,EAAE,KAAK;AACnH,UAAM,aAAa,sCAAsC,wBAAwB,KAAK;AACtF,QAAI,cAAc,wBAAwB;AACtC,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc;AAAA,MAClB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,QAAM,YAAY,CAAC,SAAS,UAAQ;AAChC,UAAM,UAAU,gBAAgB,OAAO;AACvC,UAAM,SAAS,QAAQ,KAAK,KAAK;AACjC,QAAI,QAAQ;AACR,aAAO;AAAA,QACH;AAAA,QACA,YAAY,OAAO,MAAM,CAAC;AAAA,MAC9B;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,QAAM,oBAAgB,0BAAY,CAAC,sBAAsB,uBAAqB;AAC1E,gBAAY,QAAQ,QAAQ,CAAC,EAAE,SAAS,UAAU,eAAe,OAAO,eAAe,OAAO,yBAAyB,KAAK,gBAAgB,MAAM,MAAI;AAClJ,YAAM,QAAQ,CAAC,sBAAsB,eAAe,qBAAqB,KAAK,IAAI,mBAAmB,KAAK;AAC1G,YAAM,cAAc,MAAM,QAAQ,OAAO,IAAI,UAAU;AAAA,QACnD;AAAA,MACJ;AACA,YAAM,UAAU,YAAY,IAAI,CAAC,eAAa;AAC1C,YAAI,cAAc;AACd,iBAAO,eAAe,YAAY,OAAO,sBAAsB;AAAA,QACnE;AACA,eAAO,UAAU,YAAY,KAAK;AAAA,MACtC,CAAC,EAAE,OAAO,CAAC,MAAI,CAAC;AAChB,UAAI,gBAAgB,iBAAiB,QAAQ,UAAU,GAAG;AACtD,gBAAQ,KAAK,CAAC,GAAG,MAAI,EAAE,aAAa,EAAE,UAAU;AAChD,cAAM,EAAE,SAAAC,UAAS,wBAAwB,WAAW,IAAI,QAAQ,CAAC;AACjE,iBAAS,wBAAwB,OAAO,YAAY;AAAA,UAChD,SAAAA;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL,OAAO;AACH,gBAAQ,QAAQ,CAAC,WAAS;AACtB,cAAI,OAAO,cAAc;AACrB,kBAAM,EAAE,SAAAA,UAAS,wBAAwB,WAAW,IAAI;AACxD,qBAAS,wBAAwB,OAAO,YAAY;AAAA,cAChD,SAAAA;AAAA,cACA;AAAA,YACJ,CAAC;AAAA,UACL,OAAO;AACH,kBAAM,EAAE,SAAAA,UAAS,WAAW,IAAI;AAChC,qBAAS,GAAG,YAAY;AAAA,cACpB,SAAAA;AAAA,cACA;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AAAA,EACL,GAAG;AAAA,IACC;AAAA,EACJ,CAAC;AACD,QAAM,6BAAyB,0BAAY,CAAC,sBAAsB,uBAAqB;AACnF,QAAI,cAAc;AACd,eAAS,iBAAiB,sBAAsB,kBAAkB,CAAC;AAAA,IACvE;AACA,kBAAc,sBAAsB,kBAAkB;AAAA,EAC1D,GAAG;AAAA,IACC;AAAA,IACA;AAAA,EACJ,CAAC;AACD,QAAM,4BAAwB,0BAAY,MAAI;AAC1C,QAAI,yBAAyB;AACzB,8BAAwB;AAAA,IAC5B;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,EACJ,CAAC;AACD,8BAAU,MAAI;AACV,UAAM,KAAK,kBAAkB;AAC7B,sBAAkB,WAAW;AAC7B,UAAM,YAAY;AAAA,MACd,mBAAmB;AAAA,MACnB,gCAAgC;AAAA,MAChC,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,MACnB,0CAA0C;AAAA,MAC1C,4CAA4C;AAAA,IAChD;AACA,IAAAD,oBAAmB,UAAU,IAAI,SAAS;AAC1C,WAAO,MAAI;AACP,MAAAA,oBAAmB,YAAY,EAAE;AAAA,IACrC;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACAA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,QAAM,aAAa,kBAAkB,iBAAiB,iBAAiB;AACvE,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAM,oBAAoB;AAAA,EACtB,SAAS;AAAA,EACT,eAAe,CAAC,8BAA4B;AACxC,QAAI,oBAAoB;AACpB,yBAAmB,qBAAqB,yBAAyB;AAAA,IACrE,OAAO;AACH,2BAAqB,IAAI,mBAAmB,yBAAyB;AAAA,IACzE;AACA,UAAM,0BAA0B,CAAC,CAAC,6BAA6B,yBAAyB;AACxF,wCAAoC;AACpC,0CAAsC;AAAA,EAC1C;AAAA,EACA,gBAAgB,MAAI;AAChB,QAAI,oBAAoB;AACpB,yBAAmB,qBAAqB,uBAAuB;AAAA,IACnE,OAAO;AACH,2BAAqB,IAAI,mBAAmB,uBAAuB;AAAA,IACvE;AACA,wCAAoC,CAAC,CAAC;AACtC,0CAAsC,qCAAqC,CAAC,UAAU;AAAA,EAC1F;AAAA,EACA,uBAAuB,MAAI;AACvB,QAAI,CAAC,oBAAoB;AACrB,2BAAqB,IAAI,mBAAmB,uBAAuB;AAAA,IACvE;AACA,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB,MAAI;AAChB,UAAMA,sBAAqB,kBAAkB,sBAAsB;AACnE,WAAOA,oBAAmB,eAAe;AAAA,EAC7C;AAAA,EACA,gBAAgB,CAAC,EAAE,YAAY,SAAS,IAAI,CAAC,MAAkB,oBAAoB,aAAY;AACvF,UAAMA,sBAAqB,kBAAkB,sBAAsB;AACnE,UAAMA,oBAAmB,eAAe;AAAA,MACpC;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL,CAAC,EAAE;AAAA,EACP,eAAe,MAAkB,oBAAoB,aAAY;AACzD,UAAMA,sBAAqB,kBAAkB,sBAAsB;AACnE,UAAMA,oBAAmB,cAAc;AAAA,EAC3C,CAAC,EAAE;AAAA,EACP,gBAAgB,MAAkB,oBAAoB,aAAY;AAC1D,UAAMA,sBAAqB,kBAAkB,sBAAsB;AACnE,UAAMA,oBAAmB,eAAe;AAAA,EAC5C,CAAC,EAAE;AAAA,EACP,kCAAkC,MAAI;AAAA,EACtC,oCAAoC,MAAI;AAC5C;", "names": ["debounce", "result", "self", "SpeechRecognition", "debounce", "recognitionManager", "command"]}